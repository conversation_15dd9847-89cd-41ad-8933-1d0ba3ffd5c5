import type { SVGProps } from "react"

export const GooglePlayStore = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      className="h-10"
      viewBox="0 0 204 59"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M196.396 59H7.55371C3.40106 59 0 55.6794 0 51.625V7.375C0 3.32059 3.40106 7.56029e-07 7.55371 7.56029e-07H196.396C200.549 7.56029e-07 203.95 3.32059 203.95 7.375V51.625C203.95 55.6794 200.549 59 196.396 59Z"
        fill="black"
      />
      <path
        d="M196.396 1.18184C199.894 1.18184 202.74 3.96037 202.74 7.375V51.625C202.74 55.0396 199.894 57.8182 196.396 57.8182H7.55371C4.05634 57.8182 1.21048 55.0396 1.21048 51.625V7.375C1.21048 3.96037 4.05634 1.18184 7.55371 1.18184H196.396ZM196.396 7.56029e-07H7.55371C3.40106 7.56029e-07 0 3.32059 0 7.375V51.625C0 55.6794 3.40106 59 7.55371 59H196.396C200.549 59 203.95 55.6794 203.95 51.625V7.375C203.95 3.32059 200.549 7.56029e-07 196.396 7.56029e-07Z"
        fill="#A6A6A6"
      />
      <path
        d="M71.6356 15.1095C71.6356 16.3411 71.2579 17.3276 70.5139 18.0632C69.6584 18.9353 68.5442 19.3741 67.1789 19.3741C65.874 19.3741 64.7598 18.9279 63.8458 18.0485C62.9299 17.1561 62.4729 16.0609 62.4729 14.75C62.4729 13.4391 62.9299 12.3439 63.8458 11.4589C64.7598 10.5721 65.874 10.1259 67.1789 10.1259C67.8285 10.1259 68.4479 10.2568 69.039 10.5002C69.6282 10.7454 70.1078 11.0772 70.4553 11.4866L69.6659 12.2646C69.0598 11.5658 68.2345 11.2211 67.1789 11.2211C66.2271 11.2211 65.4019 11.5456 64.7013 12.2001C64.0082 12.8565 63.6607 13.7064 63.6607 14.75C63.6607 15.7936 64.0082 16.6509 64.7013 17.3073C65.4019 17.9544 66.2271 18.2863 67.1789 18.2863C68.1892 18.2863 69.039 17.9544 69.7094 17.2999C70.1513 16.8666 70.4024 16.2692 70.4685 15.5059H67.1789V14.4402H71.5676C71.6205 14.6707 71.6356 14.8938 71.6356 15.1095Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M78.5982 11.4146H74.4757V14.2171H78.1922V15.2828H74.4757V18.0853H78.5982V19.1713H73.3087V10.3287H78.5982V11.4146Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M83.5118 19.1713H82.3448V11.4146H79.8162V10.3287H86.0423V11.4146H83.5118V19.1713Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M90.5482 19.1713V10.3287H91.7133V19.1713H90.5482Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M96.8781 19.1713H95.7205V11.4146H93.1825V10.3287H99.4161V11.4146H96.8781V19.1713Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M111.204 18.0337C110.311 18.9279 109.204 19.3741 107.884 19.3741C106.556 19.3741 105.45 18.9279 104.556 18.0337C103.665 17.1413 103.221 16.0462 103.221 14.75C103.221 13.4538 103.665 12.3587 104.556 11.4663C105.45 10.5721 106.556 10.1259 107.884 10.1259C109.196 10.1259 110.303 10.5721 111.196 11.4737C112.095 12.3734 112.539 13.4612 112.539 14.75C112.539 16.0462 112.095 17.1413 111.204 18.0337ZM105.42 17.2925C106.092 17.9544 106.909 18.2863 107.884 18.2863C108.851 18.2863 109.676 17.9544 110.341 17.2925C111.011 16.6306 111.351 15.7807 111.351 14.75C111.351 13.7193 111.011 12.8694 110.341 12.2075C109.676 11.5456 108.851 11.2137 107.884 11.2137C106.909 11.2137 106.092 11.5456 105.42 12.2075C104.749 12.8694 104.409 13.7193 104.409 14.75C104.409 15.7807 104.749 16.6306 105.42 17.2925Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M114.176 19.1713V10.3287H115.592L119.996 17.2059H120.047L119.996 15.5059V10.3287H121.161V19.1713H119.945L115.334 11.9549H115.283L115.334 13.6622V19.1713H114.176Z"
        fill="white"
        stroke="white"
        strokeWidth="0.16"
        strokeMiterlimit="10"
      />
      <path
        d="M102.934 32.085C99.386 32.085 96.4872 34.7215 96.4872 38.3593C96.4872 41.9675 99.386 44.6317 102.934 44.6317C106.49 44.6317 109.389 41.9675 109.389 38.3593C109.389 34.7215 106.49 32.085 102.934 32.085ZM102.934 42.1611C100.987 42.1611 99.3123 40.592 99.3123 38.3593C99.3123 36.097 100.987 34.5556 102.934 34.5556C104.881 34.5556 106.564 36.097 106.564 38.3593C106.564 40.592 104.881 42.1611 102.934 42.1611ZM88.8674 32.085C85.3115 32.085 82.4203 34.7215 82.4203 38.3593C82.4203 41.9675 85.3115 44.6317 88.8674 44.6317C92.4214 44.6317 95.3145 41.9675 95.3145 38.3593C95.3145 34.7215 92.4214 32.085 88.8674 32.085ZM88.8674 42.1611C86.9186 42.1611 85.2379 40.592 85.2379 38.3593C85.2379 36.097 86.9186 34.5556 88.8674 34.5556C90.8144 34.5556 92.4894 36.097 92.4894 38.3593C92.4894 40.592 90.8144 42.1611 88.8674 42.1611ZM72.1284 34.008V36.6741H78.6492C78.4584 38.1638 77.9485 39.259 77.1667 40.0223C76.215 40.9442 74.7326 41.9675 72.1284 41.9675C68.1155 41.9675 64.9732 38.8054 64.9732 34.8875C64.9732 30.9695 68.1155 27.8075 72.1284 27.8075C74.2982 27.8075 75.8769 28.6353 77.0421 29.7084L78.9664 27.8296C77.3367 26.3103 75.1688 25.1433 72.1284 25.1433C66.6255 25.1433 62.0008 29.5148 62.0008 34.8875C62.0008 40.2602 66.6255 44.6317 72.1284 44.6317C75.1027 44.6317 77.3367 43.6803 79.0929 41.8956C80.8926 40.1385 81.4535 37.6678 81.4535 35.6729C81.4535 35.0534 81.4006 34.4837 81.3062 34.008H72.1284ZM140.576 36.0748C140.046 34.6718 138.408 32.085 135.073 32.085C131.769 32.085 129.017 34.6275 129.017 38.3593C129.017 41.8734 131.74 44.6317 135.391 44.6317C138.342 44.6317 140.046 42.8746 140.746 41.8513L138.556 40.4261C137.825 41.4697 136.83 42.1611 135.391 42.1611C133.961 42.1611 132.936 41.5213 132.279 40.2602L140.873 36.7884L140.576 36.0748ZM131.814 38.1638C131.74 35.7448 133.738 34.5058 135.17 34.5058C136.291 34.5058 137.243 35.0534 137.56 35.837L131.814 38.1638ZM124.829 44.25H127.654V25.8125H124.829V44.25ZM120.202 33.4825H120.108C119.473 32.7487 118.263 32.085 116.729 32.085C113.511 32.085 110.569 34.8432 110.569 38.3795C110.569 41.8956 113.511 44.6317 116.729 44.6317C118.263 44.6317 119.473 43.9624 120.108 43.2065H120.202V44.1062C120.202 46.5049 118.89 47.7937 116.773 47.7937C115.047 47.7937 113.976 46.5768 113.534 45.5536L111.077 46.5547C111.785 48.2178 113.661 50.2643 116.773 50.2643C120.085 50.2643 122.88 48.3616 122.88 43.7319V32.4666H120.202V33.4825ZM116.971 42.1611C115.024 42.1611 113.394 40.5699 113.394 38.3795C113.394 36.1689 115.024 34.5556 116.971 34.5556C118.89 34.5556 120.402 36.1689 120.402 38.3795C120.402 40.5699 118.89 42.1611 116.971 42.1611ZM153.803 25.8125H147.046V44.25H149.864V37.2641H153.803C156.932 37.2641 160.001 35.0534 160.001 31.5374C160.001 28.0232 156.924 25.8125 153.803 25.8125ZM153.876 34.6994H149.864V28.3772H153.876C155.98 28.3772 157.181 30.0827 157.181 31.5374C157.181 32.9644 155.98 34.6994 153.876 34.6994ZM171.293 32.0499C169.258 32.0499 167.141 32.9276 166.27 34.8727L168.77 35.896C169.309 34.8727 170.298 34.5408 171.344 34.5408C172.806 34.5408 174.288 35.3982 174.311 36.9119V37.1055C173.801 36.8179 172.71 36.392 171.367 36.392C168.674 36.392 165.93 37.8393 165.93 40.5404C165.93 43.011 168.136 44.6022 170.615 44.6022C172.511 44.6022 173.558 43.767 174.215 42.7953H174.311V44.2205H177.032V37.1479C177.032 33.8789 174.532 32.0499 171.293 32.0499ZM170.954 42.1537C170.032 42.1537 168.748 41.7075 168.748 40.592C168.748 39.165 170.349 38.6174 171.735 38.6174C172.976 38.6174 173.558 38.8847 174.311 39.2369C174.09 40.9442 172.585 42.1537 170.954 42.1537ZM186.947 32.4537L183.708 40.4464H183.612L180.263 32.4537H177.225L182.256 43.6232L179.385 49.8384H182.329L190.081 32.4537H186.947ZM161.549 44.25H164.374V25.8125H161.549V44.25Z"
        fill="white"
      />
      <path
        d="M15.7646 11.1196C15.3208 11.5732 15.064 12.2793 15.064 13.1938V45.8135C15.064 46.728 15.3208 47.4341 15.7646 47.8877L15.8741 47.9873L34.596 29.7157V29.2843L15.8741 11.0127L15.7646 11.1196Z"
        fill="url(#paint0_linear_2_33)"
      />
      <path
        d="M40.8296 35.8093L34.5959 29.7157V29.2842L40.8372 23.1907L40.9769 23.2699L48.3682 27.376C50.4776 28.5412 50.4776 30.4587 48.3682 31.6313L40.9769 35.73L40.8296 35.8093Z"
        fill="url(#paint1_linear_2_33)"
      />
      <path
        d="M40.9769 35.73L34.5959 29.5L15.7646 47.8877C16.4652 48.6068 17.6077 48.6934 18.9069 47.9744L40.9769 35.73Z"
        fill="url(#paint2_linear_2_33)"
      />
      <path
        d="M40.9769 23.27L18.9069 11.0256C17.6077 10.3139 16.4652 10.4006 15.7646 11.1197L34.5959 29.5L40.9769 23.27Z"
        fill="url(#paint3_linear_2_33)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2_33"
          x1="32.9337"
          y1="46.1532"
          x2="8.18676"
          y2="20.8066"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00A0FF" />
          <stop offset="0.0066" stopColor="#00A1FF" />
          <stop offset="0.2601" stopColor="#00BEFF" />
          <stop offset="0.5122" stopColor="#00D2FF" />
          <stop offset="0.7604" stopColor="#00DFFF" />
          <stop offset="1" stopColor="#00E3FF" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_2_33"
          x1="51.115"
          y1="29.4979"
          x2="14.5597"
          y2="29.4979"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE000" />
          <stop offset="0.4087" stopColor="#FFBD00" />
          <stop offset="0.7754" stopColor="#FFA500" />
          <stop offset="1" stopColor="#FF9C00" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_2_33"
          x1="37.5071"
          y1="26.1132"
          x2="3.94827"
          y2="-8.2588"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF3A44" />
          <stop offset="1" stopColor="#C31162" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_2_33"
          x1="11.0243"
          y1="58.7401"
          x2="26.0098"
          y2="43.3916"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#32A071" />
          <stop offset="0.0685" stopColor="#2DA771" />
          <stop offset="0.4762" stopColor="#15CF74" />
          <stop offset="0.8009" stopColor="#06E775" />
          <stop offset="1" stopColor="#00F076" />
        </linearGradient>
      </defs>
    </svg>
  )
}
