import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast"
import { useToast } from "@/components/ui/use-toast"
import { CheckIcon, X } from "lucide-react"

export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(function ({
        id,
        title,
        description,
        action,
        variant,
        ...props
      }) {
        return (
          <Toast className="bg-white text-black" key={id} {...props}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && (
                <ToastDescription className="flex items-center">
                  {variant === "default" ? (
                    <>
                      <CheckIcon className="mr-2 size-4 rounded-full bg-green-500 text-xs text-white" />{" "}
                      {description}
                    </>
                  ) : (
                    <>
                      <X className="mr-2 size-4 rounded-full bg-red-500 text-xs text-white" />
                      {description}
                    </>
                  )}
                </ToastDescription>
              )}
            </div>
            {action}
            <ToastClose />
          </Toast>
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}
