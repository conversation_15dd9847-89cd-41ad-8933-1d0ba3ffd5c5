// import {
//   type LoginSchemaType,
//   type OtpSchemaType,
//   loginSchema,
//   otpSchema,
// } from "@/lib/types"
// import { useForm } from "react-hook-form"
// import { useState } from "react"
// import { zodResolver } from "@hookform/resolvers/zod"
// import { USER_LOGIN } from "@/lib/graphql/mutation"
// import { graphqlClient } from "@/lib/utils"
// import { ClientError } from "graphql-request"
// import { toast } from "../ui/use-toast"
// import { Label } from "../ui/label"
// import { Input } from "../ui/input"
// import { Button } from "../ui/button"
// import { VerifyOtpDialog } from "."
// import { useNavigate } from "react-router"
// import { useMutation } from "@tanstack/react-query"

import { useState, useEffect } from "react"
import { AppleStoreIcon } from "../icons/apple-store-icon"
import { GooglePlayStore } from "../icons/google-play-store-icon"

// NOTE: MOBILE login currently changed to only show download buttons as of 11-Aug-2025
const MobileLogin = () => {
  // const navigate = useNavigate()
  const [isIOS, setIsIOS] = useState<boolean>(false)
  const [isAndroid, setIsAndroid] = useState<boolean>(false)

  useEffect(() => {
    if (typeof window !== "undefined") {
      const ua = window.navigator.userAgent
      setIsIOS(/iPad|iPhone|iPod/.test(ua))
      setIsAndroid(/Android/.test(ua))
    }
  }, [])

  // const [openOtp, setOpenOtp] = useState(false)
  // const loginForm = useForm<LoginSchemaType>({
  //   resolver: zodResolver(loginSchema),
  // })

  // const otpForm = useForm<OtpSchemaType>({
  //   resolver: zodResolver(otpSchema),
  // })

  // const userLogin = useMutation({
  //   mutationFn: async (variables: LoginSchemaType) => {
  //     if (variables.credentials.includes("@")) {
  //       const data = await graphqlClient.request(USER_LOGIN, {
  //         email: variables.credentials,
  //       })
  //       return data
  //     } else {
  //       const data = await graphqlClient.request(USER_LOGIN, {
  //         mobileNumber: variables.credentials,
  //       })
  //       return data
  //     }
  //   },
  //   // Optional: You can define onSuccess, onError, onSettled, etc. here
  //   onSuccess: (data) => {
  //     if (data.userLogin?.otp_id) {
  //       navigate({
  //         search: "?ref=/m",
  //       })
  //       setOpenOtp(true)
  //       otpForm.setValue("otpId", data.userLogin.otp_id)
  //     }
  //   },
  //   onError: (error) => {
  //     if (error instanceof ClientError) {
  //       toast({
  //         description:
  //           error?.response?.errors?.[0]?.message || "Error validating otp",
  //       })
  //     }
  //   },
  // })

  // const handleLogin = (data: LoginSchemaType) => {
  //   void userLogin.mutate(data)
  // }
  //
  // const handleDialogChange = (state: boolean) => {
  //   setOpenOtp(state)
  // }

  return (
    <>
      <div className="mt-16 flex flex-col px-4">
        <h1 className="text-center text-2xl">Welcome</h1>
        <div className="mt-4 flex flex-col items-center gap-y-4">
          {/* Show Google Play Store badge for Android users or both for desktop */}
          {(isAndroid || (!isIOS && !isAndroid)) && (
            <a
              target="_blank"
              rel="noreferrer noopener"
              href="https://play.google.com/store/apps/details?id=in.enila.app&hl=en"
            >
              <GooglePlayStore className="h-10" />
            </a>
          )}

          {/* Show App Store badge for iOS users or both for desktop */}
          {(isIOS || (!isIOS && !isAndroid)) && (
            <a
              target="_blank"
              rel="noreferrer noopener"
              href="https://apps.apple.com/in/app/enila/id6475666829"
            >
              <AppleStoreIcon className="h-10" />
            </a>
          )}
        </div>
        {/* <h1 className="text-center text-2xl">Welcome</h1> */}
        {/* <div className="text-center">Please login to continue</div> */}
        {/* <form */}
        {/*   onSubmit={loginForm.handleSubmit(handleLogin)} */}
        {/*   className="mt-8 flex w-full flex-col space-y-2" */}
        {/* > */}
        {/*   <Label>Mobile number / Email</Label> */}
        {/*   <Input {...loginForm.register("credentials")} /> */}
        {/*   <p className="text-sm text-muted-foreground"> */}
        {/*     Verify OTP with email for regions outside India. */}
        {/*   </p> */}
        {/*   <Button type="submit" isLoading={userLogin.isPending}> */}
        {/*     Continue */}
        {/*   </Button> */}
        {/* </form> */}
      </div>
      {/* <VerifyOtpDialog */}
      {/*   open={openOtp} */}
      {/*   handleDialogChange={handleDialogChange} */}
      {/*   form={otpForm} */}
      {/*   resendOtp={() => handleLogin(loginForm.getValues())} */}
      {/* /> */}
    </>
  )
}

export default MobileLogin
