import { Rupee } from "@/components/common"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
} from "@/components/ui/dialog"
import { graphql } from "@/gql"
import { useRazorpay, useUser } from "@/lib/hooks"
import { useRentEpisodeDialogStore } from "@/lib/store"
import { graphqlClient } from "@/lib/utils"
import { useMutation } from "@tanstack/react-query"
import { useNavigate } from "react-router"

const RENT_EPISODE = graphql(`
  mutation RentEpisode($id: ID!, $redirectURL: String) {
    rentTVShowEpisode(id: $id, redirect_url: $redirectURL) {
      __typename
      ... on PhonePePaymentResponse {
        goto_url
      }
      ... on RazorPayPaymentResponse {
        order_id
      }
    }
  }
`)

const RentEpisodeDialog = () => {
  const { open, openChange, price, id, closeRentEpisodeDialog } =
    useRentEpisodeDialogStore()

  const navigate = useNavigate()
  const { handleRazorpay } = useRazorpay()
  const { data: user } = useUser()
  const rentEpisode = useMutation({
    mutationFn: async () =>
      await graphqlClient.request(RENT_EPISODE, {
        id: id,
        redirectURL: window.location.href,
      }),
    onSuccess: (data) => {
      if (data?.rentTVShowEpisode?.__typename === "PhonePePaymentResponse") {
        navigate(data.rentTVShowEpisode.goto_url)
      }
      if (data?.rentTVShowEpisode?.__typename === "RazorPayPaymentResponse") {
        handleRazorpay({
          price: price,
          orderId: data?.rentTVShowEpisode.order_id,
          user: user?.getMe,
          onLoadCallback: () => closeRentEpisodeDialog(),
          redirectUrl: window.location.href,
        })
      }
    },
  })

  const handleRentEpisode = () => {
    rentEpisode.mutate()
  }

  return (
    <Dialog open={open} onOpenChange={openChange}>
      {/* <DialogTrigger>Rent</DialogTrigger> */}
      <DialogContent>
        <DialogHeader>Disclaimer:</DialogHeader>
        <DialogDescription>
          <span className="block">
            {`The Copyright Act, 1957 (the "Act") came into effect from
                January 1958. The Act has been amended five times since then,
                i.e. in 1983, 1984, 1992, 1994, 1999 and 2012. The Copyright
                (Amendment) Act, 2012 is the most substantial.`}
          </span>
          <span className="block">
            {`Rented movie will be available for 72 hours from the time of
                purchase. Refund is not available for any item purchased. In
                case of failed payments or other issues, please contact our
                customer support.`}
          </span>
        </DialogDescription>
        <Button isLoading={rentEpisode.isPending} onClick={handleRentEpisode}>
          Rent
          <Rupee amount={price} />
        </Button>
      </DialogContent>
    </Dialog>
  )
}

export default RentEpisodeDialog
