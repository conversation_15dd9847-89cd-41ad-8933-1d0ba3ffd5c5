import { AspectRatio } from "../ui/aspect-ratio"
import { Skeleton } from "../ui/skeleton"

interface Props {
  className?: string
}

const MobileLoading = ({ className }: Props) => {
  return Array.from({ length: 4 }).map((_, i) => (
    <div key={i} className={className}>
      <AspectRatio ratio={3 / 4}>
        <Skeleton className="size-full" />
      </AspectRatio>
    </div>
  ))
}

export default MobileLoading
