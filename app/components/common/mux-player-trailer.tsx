import {
  MediaControlBar,
  MediaController,
  MediaFullscreenButton,
  MediaLoadingIndicator,
  MediaMuteButton,
  MediaPlayButton,
  MediaTimeDisplay,
  MediaTimeRange,
  MediaVolumeRange,
} from "media-chrome/react"

import {
  MediaPlaybackRateMenu,
  MediaRenditionMenu,
  MediaSettingsMenu,
  MediaSettingsMenuButton,
  MediaSettingsMenuItem,
} from "media-chrome/react/menu"

import MuxPlayer from "@mux/mux-player-react/lazy"
import { baseUrl } from "@/lib/utils"
import { usePlayVideo } from "@/lib/hooks"

interface Props {
  playbackId: string
  posterUrl: string
}

const MuxPlayerTrailer = ({ playbackId, posterUrl }: Props) => {
  const { isLoading, handlePlay } = usePlayVideo()

  return (
    <MediaController className="relative size-full">
      <MuxPlayer
        // @ts-expect-error Muxplayer doesn't allow slot media but it works
        slot="media"
        autoPlay="any"
        className="mux-player-no-controls z-40 size-full"
        playbackId={playbackId}
        streamType="on-demand"
        onLoadedData={handlePlay}
        style={{
          aspectRatio: 16 / 9,
        }}
        poster={posterUrl ? `${baseUrl}/image/medium/${posterUrl}` : ""}
        disablePictureInPicture
        loop
      />
      {isLoading ? (
        <MediaLoadingIndicator
          suppressHydrationWarning
          noautohide
          className="z-40"
          slot="centered-chrome"
          style={{ "--media-loading-indicator-icon-height": "200px" }}
        ></MediaLoadingIndicator>
      ) : (
        <MediaPlayButton
          style={{
            "--media-button-icon-height": "100px",
            "--media-button-icon-width": "100px",
          }}
          className="z-40 size-full bg-transparent focus:outline-none"
          slot="centered-chrome"
        ></MediaPlayButton>
      )}
      <MediaControlBar className="z-50">
        <MediaSettingsMenu className="z-50" hidden anchor="auto">
          <MediaSettingsMenuItem>
            Speed
            <MediaPlaybackRateMenu slot="submenu" hidden>
              <div slot="title">Speed</div>
            </MediaPlaybackRateMenu>
          </MediaSettingsMenuItem>
          <MediaSettingsMenuItem>
            Quality
            <MediaRenditionMenu slot="submenu" hidden>
              <div slot="title">Quality</div>
            </MediaRenditionMenu>
          </MediaSettingsMenuItem>
        </MediaSettingsMenu>
        <MediaPlayButton></MediaPlayButton>
        <MediaMuteButton></MediaMuteButton>
        <MediaVolumeRange />
        <MediaTimeRange></MediaTimeRange>
        <MediaTimeDisplay showDuration></MediaTimeDisplay>
        <MediaSettingsMenuButton></MediaSettingsMenuButton>
        <MediaFullscreenButton></MediaFullscreenButton>
      </MediaControlBar>
    </MediaController>
  )
}

export default MuxPlayerTrailer
