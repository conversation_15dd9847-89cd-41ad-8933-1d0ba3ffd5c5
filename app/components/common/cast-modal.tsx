import { cn } from "@/lib/utils/cn"
import { Dialog, DialogContent, DialogTrigger } from "../ui/dialog"
import { buttonVariants } from "../ui/button"
import { ScrollArea } from "../ui/scroll-area"
import { useState } from "react"

interface Props {
  cast?: string
}

const CastModal = ({ cast }: Props) => {
  const [open, setOpen] = useState(false)
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger
        className={cn(
          buttonVariants({ variant: "link" }),
          "my-0 h-auto px-0 py-0"
        )}
      >
        See More
      </DialogTrigger>
      <DialogContent
        onClick={() => setOpen(false)}
        className="max-w-screen flex h-screen w-full flex-col items-center justify-center border-0 bg-transparent"
      >
        <ScrollArea
          onClick={(e) => e.stopPropagation()}
          className="size-full max-h-80 max-w-72"
        >
          <div className="flex size-full flex-col items-center justify-center whitespace-pre-wrap text-center">
            {cast}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}

export default CastModal
