import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>itle,
} from "@/components/ui/dialog"
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp"
import { graphqlClient } from "@/lib/utils"
import { type UseFormReturn } from "react-hook-form"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import { useEffect, useState } from "react"
import { OTP_RESEND_TIMER } from "@/lib/utils/constants"
import { useFetcher} from "react-router"
import { useToast } from "@/components/ui/use-toast"
import { ClientError } from "graphql-request"
import { VERIFY_OTP } from "@/lib/graphql/mutation"
import { type OtpSchemaType } from "@/lib/types"
import { useQueryClient, useMutation } from "@tanstack/react-query"

interface Props {
  open: boolean
  handleDialogChange: (state: boolean) => void
  form: UseFormReturn<OtpSchemaType>
  resendOtp: () => void
}

const VerifyOtpDialog = ({
  open,
  handleDialogChange,
  form,
  resendOtp,
}: Props) => {
  const fetcher = useFetcher()
  const queryClient = useQueryClient()
  const { toast } = useToast()
  const [disableResend, setDisableResend] = useState(true)
  const [timeLeft, setTimeLeft] = useState(OTP_RESEND_TIMER)

  const verifyOtp = useMutation({
    mutationFn: async (variables: OtpSchemaType) => {
      const data = await graphqlClient.request(VERIFY_OTP, {
        otpId: variables.otpId,
        code: parseInt(variables.code),
      })
      return data
    },
    onSuccess: async (data) => {
      if (data.userLoginVerifyOTP?.token) {
        await queryClient.refetchQueries()
        // Cookies.set("user-token", data.userLoginVerifyOTP.token)
        toast({
          description: "Login successful",
          variant: "default",
        })

        const token = data.userLoginVerifyOTP.token
        const formData = new FormData()
        formData.append('token', token)

        void fetcher.submit(formData, {
          method: 'POST',
        })


        // TODO: use ref to return to other places
        // Check if there's a 'ref' parameter
        // const ref = searchParams.get("ref")
        //
        // // If 'ref' exists, navigate to its value. Otherwise, navigate to the home page
        // if (ref) {
        //   window.location.href = ref
        // } else {
        //   window.location.href = "/"
        // }
      }
    },
    onError: (error) => {
      if (error instanceof ClientError) {
        if (error?.response?.errors) {
          form.setError("code", {
            type: "custom",
            message: error?.response.errors[0].message,
          })
        }
      }
      console.log(error)
    },
  })

  const handleVerify = (data: OtpSchemaType) => {
    verifyOtp.mutate(data)
  }

  useEffect(() => {
    let timer: NodeJS.Timeout
    if (open) {
      timer = setInterval(() => {
        if (timeLeft === 0) {
          setDisableResend(false)
          clearInterval(timer)
        } else {
          setTimeLeft((time) => time - 1)
        }
      }, 1000)
    }

    return () => {
      clearInterval(timer)
    }
  }, [open, timeLeft])

  return (
    <Dialog open={open} onOpenChange={handleDialogChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Verify OTP</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            className="space-y-4"
            onSubmit={form.handleSubmit(handleVerify)}
          >
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>One-Time Password</FormLabel>
                  <FormControl>
                    <InputOTP maxLength={6} {...field}>
                      <InputOTPGroup>
                        <InputOTPSlot index={0} />
                        <InputOTPSlot index={1} />
                        <InputOTPSlot index={2} />
                        <InputOTPSlot index={3} />
                        <InputOTPSlot index={4} />
                        <InputOTPSlot index={5} />
                      </InputOTPGroup>
                    </InputOTP>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-between">
              <div>
                <Button
                  disabled={disableResend}
                  type="button"
                  variant="link"
                  onClick={() => {
                    resendOtp()
                    setDisableResend(true)
                    setTimeLeft(OTP_RESEND_TIMER)
                  }}
                >
                  Resend OTP {timeLeft > 0 ? `- ${timeLeft}s` : null}
                </Button>
              </div>
              <div className="space-x-4">
                <Button
                  variant="secondary"
                  type="button"
                  onClick={() => handleDialogChange(false)}
                >
                  Cancel
                </Button>
                <Button isLoading={verifyOtp.isPending} type="submit">
                  Submit
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
export default VerifyOtpDialog
