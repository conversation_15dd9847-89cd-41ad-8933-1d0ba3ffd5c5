import { decodeBlurhash } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"
import { useState } from "react"

interface Props {
  src: string
  alt: string
  blurhash: string
  className?: string
}

const BlurhashImage = ({ src, alt, blurhash, className }: Props) => {
  const [loaded, setLoaded] = useState(false)
  return (
    <div className={cn("h-full w-full object-cover", className)}>
      <div
        className={cn(
          "absolute inset-0 h-full w-full blur-lg transition-opacity duration-500 ease-in-out",
          {
            "opacity-0": loaded,
          }
        )}
        style={{
          backgroundImage: `url(${decodeBlurhash(blurhash)})`,
          backgroundSize: "cover",
          backgroundRepeat: "no-repeat",
        }}
      />
      <img
        className={cn(
          "absolute inset-0 h-full w-full object-cover transition ease-in-out",
          {
            "opacity-100 blur-none": loaded,
            "opacity-0 blur-lg": !loaded,
          }
        )}
        loading="lazy"
        src={src}
        alt={alt}
        onLoad={() => setLoaded(true)}
        style={{
          transitionProperty: "opacity, filter",
          transitionDuration: "500ms, 1500ms",
        }}
      />
    </div>
  )
}

export default BlurhashImage
