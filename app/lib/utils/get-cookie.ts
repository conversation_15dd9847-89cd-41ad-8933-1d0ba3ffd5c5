const getCookieValue = (cookieName: string, cookieString: string): string => {
  const cookiesArray = cookieString.split(";").map((cookie) => cookie.trim())
  const cookiesHashmap: Record<string, string> = cookiesArray.reduce(
    (all, cookie) => {
      const [name, value] = cookie.split("=")
      return { ...all, [name]: value }
    },
    {}
  )

  return cookiesHashmap[cookieName] || ""
}

export default getCookieValue
