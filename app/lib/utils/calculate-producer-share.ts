import type { StatsData } from "@/gql/graphql"
import { formatIndianCurrency } from "./format-currency"

interface Props {
  producerShare: number
  stats: StatsData[]
  returnType?: "string" | "number"
}

const CalculateProducerShare = ({
  producerShare,
  stats,
  returnType = "string",
}: Props) => {
  const producerShareValue = producerShare * 0.01
  const totalRevenue = stats.reduce((total, current) => {
    return total + current.total_revenue
  }, 0)

  const totalProducerShare = totalRevenue * producerShareValue

  const formattedShare = formatIndianCurrency(totalProducerShare)

  if (returnType === "string") {
    return formattedShare
  } else {
    return totalProducerShare
  }
}

export default CalculateProducerShare
