export default function getTokenFromUrl(url: string) {
  const parts = url.split("?")

  // If there are query parameters after "?", split them by "&"
  const queryParameters = parts.length > 1 ? parts[1].split("&") : []

  // Search for the "token=" parameter
  for (const parameter of queryParameters) {
    if (parameter.startsWith("token=")) {
      // If found, return the token value (remove "token=" prefix)
      return parameter.slice("token=".length)
    }
  }

  // If no token is found, return null or an appropriate value
  return null
}
