import { decode } from "blurhash"

const decodeBlurhash = (blurhash?: string) => {
  // if (typeof window === "undefined") {
  //   return undefined
  // }
  //
  if (!blurhash) {
    return undefined
  }

  const pixels = decode(blurhash, 32, 32)
  let canvas = document.createElement("canvas")
  let ctx = canvas.getContext("2d")

  canvas.width = 32
  canvas.height = 32

  if (!ctx) {
    return undefined
  }

  let imageData = ctx.createImageData(32, 32)
  imageData.data.set(pixels)
  ctx.putImageData(imageData, 0, 0)

  return canvas.toDataURL()
}

export default decodeBlurhash
