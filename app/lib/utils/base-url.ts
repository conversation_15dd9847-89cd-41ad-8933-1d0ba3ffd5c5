// const baseUrl =
//   process.env.NODE_ENV === "production" ? "" : "https://api-dev.enila.in"
const getBaseUrl = () => {
  if (process.env.NODE_ENV === "production") {
    return "https://api.enila.in"
  } else if (process.env.NODE_ENV === "test") {
    return "https://api-dev.enila.in"
  } else {
    // return "https://api.enila.in" // production
    return "https://api-dev.enila.in" // staging
    // return "http://localhost:8002" // Default for development
  }
}

const baseUrl = getBaseUrl()

export default baseUrl
