import { graphql } from "@/gql"

const RENT_MULTIPLE_EPISODES = graphql(`
  mutation RentMultipleEpisodes($ids: [ID!]!, $totalAmount: Float!) {
    rentMultipleEpisodes(ids: $ids, total_amount: $totalAmount) {
      __typename
      ... on PhonePePaymentResponse {
        goto_url
      }
      ... on RazorPayPaymentResponse {
        order_id
      }
    }
  }
`)

export default RENT_MULTIPLE_EPISODES
