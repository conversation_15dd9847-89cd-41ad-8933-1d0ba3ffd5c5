import { graphql } from "@/gql"

const SUBSCRIBE_TO_PLAN = graphql(`
  mutation SubscribeToPlan($planId: ID!, $redirectUrl: String) {
    subscribeToPlan(plan_id: $planId, redirect_url: $redirectUrl) {
      __typename
      ... on PhonePePaymentResponse {
        goto_url
      }
      ... on RazorPayPaymentResponse {
        order_id
        price
      }
    }
  }
`)

export default SUBSCRIBE_TO_PLAN
