import { graphql } from "@/gql"

const UPSERT_WATCHED_DURATION = graphql(`
  mutation UpsertWatchedDuration(
    $contentId: ID!
    $contentType: WatchDurationContentType!
    $duration: Int!
    $totalDuration: Int!
  ) {
    upsertWatchedDuration(
      content_id: $contentId
      content_type: $contentType
      duration: $duration
      total_duration: $totalDuration
    ) {
      message
    }
  }
`)

export default UPSERT_WATCHED_DURATION
