import { graphql } from "@/gql"

const SHOW_BY_SLUG_QUERY = graphql(`
  query TvShowBySlug($slug: String!) {
    getTvShowBySlug(slug: $slug) {
      id
      title
      description_en
      description_mz
      age_label
      production_year
      genre
      casts
      price_per_episode
      imageLandscape {
        id
        path
        hash
      }
      seasons {
        id
        title
        # trailer_url
        # price
        episodes {
          id
          cdn_playback_id
        }
      }
    }
  }
`)

export default SHOW_BY_SLUG_QUERY
