import { graphql } from "@/gql";

const GET_PAYMENT_STATUS_QUERY = graphql(`
   query GetPaymentStatus($orderID: String!) {
     getPaymentStatus(order_id: $orderID, provider: RZPAY) {
       status
     }
   } 
`)

export default GET_PAYMENT_STATUS_QUERY

// import { graphql } from '$houdini'
//
// interface Props {
// 	orderID: string
// }
//
// export const handlePaymentStatus = async ({ orderID }: Props) => {
// 	const getPaymentStatus = graphql(`
// 		query GetPaymentStatus($orderID: String!) @cache(policy: NetworkOnly) {
// 			getPaymentStatus(order_id: $orderID, provider: RZPAY) {
// 				status
// 			}
// 		}
// 	`)
//
// 	const query = await getPaymentStatus.fetch({
// 		variables: {
// 			orderID: orderID
// 		}
// 	})
//
// 	const status = query.data?.getPaymentStatus?.status
//
// 	return { status }
// }
//
