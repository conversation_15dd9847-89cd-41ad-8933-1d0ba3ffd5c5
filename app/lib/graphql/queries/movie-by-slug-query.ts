import { graphql } from "@/gql"

const MOVIE_BY_SLUG_QUERY = graphql(`
  query MovieBySlug($slug: String!) {
    getMovieBySlug(slug: $slug) {
      id
      title
      subtitle_path_en
      subtitle_path_mz
      description_en
      description_mz
      duration
      age_label
      casts
      production_year
      production_year
      genre
      price
      # trailer_url
      imagePortrait {
        id
        path
        hash
      }
      imageLandscape {
        id
        path
        hash
      }
      is_rented
      cdn_playback_id
      for_subscriber
      is_free
      isAddedToWatchList
      trailer {
        cdn_playback_id
      }
      continueWatching {
        watched_duration
      }
      ...MovieAction
    }
  }
`)

export default MOVIE_BY_SLUG_QUERY
