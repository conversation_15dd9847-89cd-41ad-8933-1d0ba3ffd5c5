import { graphql } from "@/gql";

const MovieFragment = graphql(`
  fragment MovieFragment on Movie {
    id
    title
    description_en
    description_mz
    production_year
    age_label
    duration
    genre
    slug
    imageLandscape {
      id
      path
      hash
    }
    imagePortrait {
      id
      path
      hash
    }
    imageSquare {
      id
      path
      hash
    }
    imageSquare2x {
      id
      path
      hash
    }
    imagePortrait2x {
      id
      path
      hash
    }
  }
`)

export default MovieFragment
