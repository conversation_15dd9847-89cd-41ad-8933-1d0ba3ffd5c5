import { graphql } from "@/gql";

const TvShowFragment = graphql(`
  fragment TvShowFragment on TVShow {
    id
    title
    slug
    description_en
    description_mz
    production_year
    genre
    age_label
    imageLandscape {
      id
      path
      hash
    }
    imagePortrait {
      id
      path
      hash
    }
    imageSquare {
      id
      path
      hash
    }
    imagePortrait2x {
      id
      path
      hash
    }
    imageSquare2x {
      id
      path
      hash
    }
  }
`)

export default TvShowFragment
