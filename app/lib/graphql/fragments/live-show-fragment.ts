import { graphql } from "@/gql"

const LIVE_SHOW_FRAGMENT = graphql(`
  fragment LiveShowFragment on Live {
    id
    title
    slug
    cdn_playback_id
    cdn_content_status
    is_rented
    is_free
    for_subscriber
    price
    description_en
    description_mz
    imageLandscape {
      id
      path
      hash
    }
    imagePortrait {
      id
      path
      hash
    }
  }
`)

export default LIVE_SHOW_FRAGMENT
