import { graphql } from "@/gql"

const HOME_DATA_FRAGMENT = graphql(`
  fragment HomeDataFragment on HomeData {
    id
    category
    order
    display_type
    homeDataList {
      id
      posterLandscapeImage {
        id
        path
        hash
      }
      titleImage {
        id
        path
        hash
      }
      homeable {
        __typename
        ...TvShowFragment
        ...LiveShowFragment
        ...MovieFragment
      }
    }
  }
`)

export default HOME_DATA_FRAGMENT
