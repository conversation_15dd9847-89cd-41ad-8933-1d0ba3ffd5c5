import { graphql } from "@/gql"

const EPISODE_FRAGMENT = graphql(`
  fragment TVEpisodeFragment on TVShowEpisodes {
    __typename
    id
    cdn_playback_id
    isAddedToWatchList
    title
    sub_header
    duration
    is_rented
    is_free
    description_en
    description_mz
    for_subscriber
    tv_show_id
    tv_show_season_id
    continueWatching {
      watched_duration
      total_duration
    }
    imageLandscape {
      id
      path
      hash
    }
  }
`)

export default EPISODE_FRAGMENT
