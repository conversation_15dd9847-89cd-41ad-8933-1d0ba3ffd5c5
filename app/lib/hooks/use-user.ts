import { graphqlClient } from "../utils"
import { GET_ME } from "../graphql/queries"
import { useLanguage } from "../store"
import { useQuery } from "@tanstack/react-query"
import { useEffect } from "react"

export const useUser = () => {
  const { setLanguage } = useLanguage()
  const { data, isError, isLoading } = useQuery({
    queryKey: ["get-user"],
    queryFn: async () => {
      return await graphqlClient.request(GET_ME)
    },
  })

  useEffect(() => {
    if (data?.getMe) {
      setLanguage(data?.getMe.language || "en")
    }
  }, [data, setLanguage])

  return { data, isError, isLoading }
}
