import { ADD_WATCH_LIST, DELETE_MY_WATCH_LIST } from "../graphql/mutation"
import { WatchListableType } from "@/gql/graphql"
import { graphqlClient } from "../utils"
import { useMutation } from "@tanstack/react-query"

interface AddProps {
  id: string
  contentType: WatchListableType
}

interface DeleteProps {
  id: string
}

export const useWatchList = () => {
  const addToWatchList = useMutation(
    {
      mutationFn: async (variables: AddProps) => {
        return await graphqlClient.request(ADD_WATCH_LIST, {
          id: variables.id,
          contentType: variables.contentType,
        })
      },
    }
  )

  const deleteFromWatchList = useMutation({
    mutationFn: async (variables: DeleteProps) =>
      await graphqlClient.request(DELETE_MY_WATCH_LIST, {
        id: variables.id,
      }),
  })

  return { addToWatchList, deleteFromWatchList }
}
