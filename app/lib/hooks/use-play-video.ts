import { useNavigate } from "react-router"
import { useState } from "react"

export const usePlayVideo = () => {
  const [isLoading, setIsLoading] = useState(true)
  const [isFullScreen, setIsFullScreen] = useState(false)
  const navigate = useNavigate()

  const handleFullScreen = async () => {
    if (isFullScreen) {
      await document.exitFullscreen()
    } else {
      await document.body.requestFullscreen()
    }

    setIsFullScreen(!isFullScreen)
  }

  const handleBackButton = async () => {
    if (isFullScreen) {
      await document.exitFullscreen()
      setIsFullScreen(false)
      void navigate(-1)
    } else {
      void navigate(-1)
    }
  }

  // useful for setting loading indicator and play button to share centered-chrome slot
  const handlePlay = (e: Event) => {
    if (e) {
      setIsLoading(false)
    }
  }

  return {
    isLoading,
    isFullScreen,
    handlePlay,
    handleFullScreen,
    handleBackButton,
  }
}
