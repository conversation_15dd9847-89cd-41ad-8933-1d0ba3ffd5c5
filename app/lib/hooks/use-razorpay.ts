import type { User } from "@/gql/graphql"
import { graphqlClient, razorpayKey } from "@/lib/utils"
import { useNavigate } from "react-router"
import type { RecursivePartial } from "../types"
import { GET_PAYMENT_STATUS_QUERY } from "../graphql/queries"
import { useGlobalLoadingStore } from "../store"

interface Props {
  orderId: string
  price: number
  user?: RecursivePartial<User> | null
  contact?: string
  email?: string
  onLoadCallback?: () => void
  backdropFn?: () => void
  callbackUrl?: string // for mobile view
  gotoUrl?: string | null // for using navigate redirect
  redirectUrl?: string // for when using window to redirect
}

export const useRazorpay = () => {
  const navigate = useNavigate()
  const { setGlobalLoading } = useGlobalLoadingStore()
  const handleRazorpay = ({
    orderId,
    price,
    user,
    contact,
    onLoadCallback,
    backdropFn,
    callbackUrl,
    gotoUrl,
    redirectUrl,
    email,
  }: Props) => {
    backdropFn && backdropFn()
    const rzpAmount = price * 100
    const options = {
      key: razorpayKey,
      amount: rzpAmount, // in currency subunits. Here 1000 = 1000 paise, which equals to Γé╣10
      config: {
        display: {
          blocks: {
            card: {
              instruments: [
                {
                  method: "card",
                },
              ],
              name: "Pay with card",
            },
            // netbanking: false,
            upi: {
              instruments: [
                {
                  apps: ["google_pay", "paytm", "phonepe", "bhim", "amazon", "airtel", "sbi"],
                  flows: ["qr", "collect", "intent"],
                  method: "upi",
                },
              ],
              name: "Pay with UPI",
            },
            wallet: {
              instruments: [
                {
                  method: "wallet",
                },
              ],
              name: "Pay with wallet",
            },
          },
          preferences: {
            show_default_blocks: true,
          },
          sequence: ["block.upi", "card", "wallet"],
        },
      },
      currency: "INR", // Default is INR. We support more than 90 currencies.
      image: "/enila_logo.png",
      name: "ENILA",
      order_id: orderId, // Replace with Order ID generated in Step 4
      theme: {
        color: "#fcb510",
      },
      prefill: {
        contact: user?.mobile_number ?? contact ?? "",
        name: user?.username ?? "",
        email: user?.email ?? email ?? "",
      },
      callback_url: callbackUrl,
      redirect: callbackUrl ? true : false,
      modal: {
        ondismiss: function () {
          if (callbackUrl) {
            window.location.href = "/mobile-payment/dialog-close"
          }
        },
      },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      handler: function (response: any) {
        // for loading state
        // pollingRazorpay.polling()
        setGlobalLoading(true)

        let count = 0
        const intervalId = setInterval(async () => {
          try {
            // eslint-disable-next-line
            const status: any = await handlePaymentStatus({
              orderID: response.razorpay_order_id,
            })

            if (status === "success") {
              clearInterval(intervalId)
              // window.location.reload()
              if (redirectUrl) {
                window.location.href = redirectUrl
              } else if (gotoUrl) {
                navigate(gotoUrl)
              }
              // pollingRazorpay.notPolling()
              setGlobalLoading(false)
            } else {
              count += 1
            }

            if (count >= 20) {
              // pollingRazorpay.notPolling()
              setGlobalLoading(false)
              clearInterval(intervalId)
              navigate("/payment-failure")
            }
          } catch (err) {
            // pollingRazorpay.notPolling()
            setGlobalLoading(false)
            clearInterval(intervalId)
            navigate("/payment-failure")
          }
        }, 3000)
      },

      method: "upi",
      apps: ["google_pay", "phonepe", "bhim", "paytm", "sbi"],

      // method specific fields
      // bank: 'HDFC'
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const razorpay = new (window as any).Razorpay(options)

    razorpay.once("ready", function () {
      onLoadCallback && onLoadCallback()
      backdropFn && backdropFn()
      razorpay.open()
      // setOpen(false)
    })

    // eslint-disable-next-line
    razorpay.on("payment.failed", function (response: any) {
      // toast.error(response.error.description)
      // setTotalAmount(0)
      // setTotalAmountPayable(0)
      // setTicketValue(0)
      // setConvenienceAmount(0)
      // form.reset()
      console.log("rzpay payment failed", response)
      razorpay.close()
      navigate("/payment-failure")
    })
    // eslint-disable-next-line
    razorpay.on("payment.error", function (response: any) {
      console.log("rzpay payment error", response)
      razorpay.close()
      navigate("/payment-failure")
    })
  }

  return { handleRazorpay }
}

const handlePaymentStatus = async ({ orderID }: { orderID: string }) => {
  const response = await graphqlClient.request(GET_PAYMENT_STATUS_QUERY, {
    orderID: orderID,
  })

  return response?.getPaymentStatus?.status
}
