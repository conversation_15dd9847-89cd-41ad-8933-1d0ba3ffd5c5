import { create } from "zustand"
import { createJSONStorage, persist } from "zustand/middleware"

type LanguageState = {
  language: string
  setLanguage: (language: string) => void
}

export const useLanguage = create(
  persist<LanguageState>(
    (set) => ({
      language: "en",
      setLanguage: (language: string) => set({ language }),
    }),
    {
      name: "language",
      storage: createJSONStorage(() => localStorage),
    }
  )
)
