import { create } from "zustand"

type LiveRentState = {
  open: boolean
  id: string
  price: number
  openLive: (id: string, price: number) => void
  closeLive: () => void
  openChange: (open: boolean) => void
}

export const useLiveRentDialogStore = create<LiveRentState>((set) => ({
  open: false,
  id: "",
  price: 0,
  openLive: (id: string, price: number) =>
    set({ open: true, id: id, price: price }),
  closeLive: () => set({ open: false, id: "", price: 0 }),
  openChange: (open: boolean) => set({ open }),
}))
