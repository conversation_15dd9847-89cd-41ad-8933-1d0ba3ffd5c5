import { create } from "zustand"

type EpisodeRentState = {
  open: boolean
  id: string
  price: number
  openRentEpisodeDialog: (id: string, price: number) => void
  closeRentEpisodeDialog: () => void
  openChange: (open: boolean) => void
}

export const useRentEpisodeDialogStore = create<EpisodeRentState>((set) => ({
  open: false,
  id: "",
  price: 0,
  openRentEpisodeDialog(id, price) {
    set({ open: true, id: id, price: price })
  },
  closeRentEpisodeDialog() {
    set({ open: false, id: "", price: 0 })
  },
  openChange(open) {
    set({ open })
  },
}))
