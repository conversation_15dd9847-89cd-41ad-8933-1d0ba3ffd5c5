import { Rupee } from "@/components/common"
import { Button } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { graphql } from "@/gql"
import type { TvShowEpisodes } from "@/gql/graphql"
import { RENT_MULTIPLE_EPISODES } from "@/lib/graphql/mutation"
import { useRazorpay, useUser } from "@/lib/hooks"
import { graphqlClient } from "@/lib/utils"
import { useNavigate } from "react-router"
import { useState } from "react"
import { useMutation, useQuery } from "@tanstack/react-query"

interface Props {
  open: boolean
  handleOpenChange: (open: boolean) => void
  showId: string
}

const TV_SHOW_BY_ID = graphql(`
  query TvShowById($id: ID!) {
    tvShowById(id: $id) {
      id
      title
      price_per_episode
      description_mz
      description_en
      seasons {
        id
        title
        episodes {
          id
          title
          sub_header
          is_free
          for_subscriber
          is_rented
        }
      }
    }
  }
`)

const TVDetailDialog = ({ showId, open, handleOpenChange }: Props) => {
  const [selectedSeason, setSelectedSeason] = useState("")
  const [selectedEpisodes, setSelectedEpisodes] = useState<string[]>([])
  const [totalAmount, setTotalAmount] = useState(0)

  const navigate = useNavigate()

  const { handleRazorpay } = useRazorpay()

  const { data: user } = useUser()

  const { data, isLoading, isError } = useQuery({
    queryKey: ["season-by-id", showId],
    queryFn: async () =>
      graphqlClient.request(TV_SHOW_BY_ID, {
        id: showId,
      }),
    enabled: !!showId,
  })

  const rentMultipleEpisodes = useMutation({
    mutationFn: async () => {
      return await graphqlClient.request(RENT_MULTIPLE_EPISODES, {
        ids: selectedEpisodes,
        totalAmount: totalAmount,
      })
    },
    onSuccess: (data) => {
      if (data?.rentMultipleEpisodes?.__typename === "PhonePePaymentResponse") {
        navigate(data.rentMultipleEpisodes.goto_url)
      }
      if (
        data?.rentMultipleEpisodes?.__typename === "RazorPayPaymentResponse"
      ) {
        handleRazorpay({
          price: totalAmount,
          orderId: data?.rentMultipleEpisodes.order_id,
          user: user?.getMe,
          onLoadCallback: () => handleDialog(false),
          redirectUrl: window.location.href,
        })
      }
    },
  })

  const handleSelectEpisodes = (id: string) => {
    let newSelectedIds

    if (selectedEpisodes.includes(id)) {
      newSelectedIds = selectedEpisodes.filter((i) => i !== id)
    } else {
      newSelectedIds = [...selectedEpisodes, id]
    }

    const pricePerEpisode = data?.tvShowById?.price_per_episode || 0
    const totalAmount = newSelectedIds.length * pricePerEpisode

    setSelectedEpisodes(newSelectedIds)
    setTotalAmount(totalAmount)
  }

  const handleSelectSeason = (id: string) => {
    setSelectedEpisodes([])
    setSelectedSeason(id)
    setTotalAmount(0)
  }

  // const handleSelectAll = () => {
  //   // handle select all
  //   if (selectedEpisodes.length === selectedSeasonEpisodes?.length) {
  //     setSelectedEpisodes([])
  //     setTotalAmount(0)
  //   } else {
  //     const allEpisodeIds = selectedSeasonEpisodes.map((episode) =>
  //       episode ? episode.id : ""
  //     )
  //     const pricePerEpisode = data?.tvShowById?.price_per_episode || 0
  //     const totalAmount = allEpisodeIds.length * pricePerEpisode
  //
  //     setSelectedEpisodes(allEpisodeIds)
  //     setTotalAmount(totalAmount)
  //   }
  // }

  const handleRentEpisodes = () => {
    rentMultipleEpisodes.mutate()
  }

  const handleDialog = (open: boolean) => {
    handleOpenChange(open)
    setSelectedEpisodes([])
    setSelectedSeason("")
    setTotalAmount(0)
  }

  const selectedSeasonEpisodes =
    data?.tvShowById?.seasons?.find((season) => season?.id === selectedSeason)
      ?.episodes || []

  return (
    <Dialog open={open} onOpenChange={handleDialog}>
      <DialogContent>
        <DialogHeader>{data?.tvShowById?.title}</DialogHeader>
        <Select value={selectedSeason} onValueChange={handleSelectSeason}>
          <SelectTrigger>
            <SelectValue placeholder="Select season" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              {data?.tvShowById?.seasons?.map((season) => {
                return (
                  season && (
                    <SelectItem key={season?.id} value={season?.id}>
                      {season?.title}
                    </SelectItem>
                  )
                )
              })}
            </SelectGroup>
          </SelectContent>
        </Select>
        {selectedSeasonEpisodes?.length === 0 && (
          <DialogDescription className={"text-sm"}>
            Purchase content can be watched with EN ILA mobile app available on
            the App store or Play store or a desktop browser.
            <br />
            Purchase will be available for 72 hours from the time of purchase.
            Refund is not available for any item purchased. In case of failed
            payments or other issues, please contact our customer support.
            <br />
            For more information refer to our{" "}
            <a href="/refund-policy" className="text-theme-blue">
              refund policy
            </a>
          </DialogDescription>
        )}
        {selectedSeasonEpisodes?.length > 0 ? (
          <ScrollArea className="h-64">
            <div className="space-y-4 px-4">
              {/* <div className="flex gap-x-4"> */}
              {/*   <input */}
              {/*     id="select-all" */}
              {/*     type="checkbox" */}
              {/*     onChange={handleSelectAll} */}
              {/*     checked={ */}
              {/*       selectedEpisodes.length === selectedSeasonEpisodes.length */}
              {/*     } */}
              {/*   /> */}
              {/*   <label htmlFor="select-all">Select all</label> */}
              {/* </div> */}
              {selectedSeasonEpisodes && (
                <EpisodeList
                  handleSelectEpisodes={handleSelectEpisodes}
                  selectedEpisodes={selectedEpisodes}
                  episodes={selectedSeasonEpisodes}
                  pricePerEpisode={data?.tvShowById?.price_per_episode || 0}
                />
              )}
            </div>
          </ScrollArea>
        ) : null}
        <Button
          onClick={handleRentEpisodes}
          isLoading={rentMultipleEpisodes.isLoading}
          disabled={
            rentMultipleEpisodes.isPending ||
            selectedSeasonEpisodes.length === 0
          }
        >
          Rent {totalAmount > 0 && <Rupee amount={totalAmount} />}
        </Button>
      </DialogContent>
    </Dialog>
  )
}

interface EpisodeListProps {
  episodes: Partial<TvShowEpisodes | null>[]
  handleSelectEpisodes: (id: string) => void
  selectedEpisodes: string[]
  pricePerEpisode: number
}

const EpisodeList = ({
  episodes,
  handleSelectEpisodes,
  selectedEpisodes,
  pricePerEpisode,
}: EpisodeListProps) => {
  return episodes?.map((episode) => {
    let status
    const disabled =
      episode?.is_rented || episode?.for_subscriber || episode?.is_free

    if (episode?.for_subscriber) {
      if (episode?.is_rented) {
        status = "Rented"
      } else {
        status = "Require subscription"
      }
    } else if (episode?.is_free) {
      status = "Free"
    } else if (episode?.is_rented) {
      status = "Rented"
    } else {
      status = pricePerEpisode
    }

    return (
      episode && (
        <div key={episode?.id} className="flex gap-x-4">
          <input
            onChange={() => handleSelectEpisodes(episode.id!)}
            id={episode?.id}
            type="checkbox"
            disabled={disabled}
            checked={selectedEpisodes.includes(episode.id!)}
          />
          <label
            className="flex w-full items-center justify-between"
            htmlFor={episode?.id}
          >
            <div className="flex flex-col">
              <span>{episode?.title}</span>
              <span className="text-xs text-muted-foreground">
                {episode?.sub_header}
              </span>
            </div>
            <div>
              {typeof status === "string" ? status : <Rupee amount={status} />}
            </div>
          </label>
        </div>
      )
    )
  })
}

export default TVDetailDialog
