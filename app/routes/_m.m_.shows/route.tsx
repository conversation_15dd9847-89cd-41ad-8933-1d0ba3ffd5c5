import { BlurhashImage, MobileError, MobileLoading } from "@/components/common"
import { SearchIcon } from "@/components/icons"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { graphql } from "@/gql"
import { baseUrl, graphqlClient } from "@/lib/utils"
import { DEBOUNCE_TIMER, MOBILE_FETCH_NUMBER } from "@/lib/utils/constants"
import { useState } from "react"
import { useDebounce } from "use-debounce"
import TVDetailDialog from "./tv-detail-dialog"
import { useInfiniteQuery } from "@tanstack/react-query"
import type { TvShowsQuery } from "@/gql/graphql"

const TV_SHOWS = graphql(`
  query TvShows($first: Int, $page: Int, $keyword: String) {
    getTvShows(first: $first, page: $page, keyword: $keyword) {
      data {
        id
        title
        imagePortrait {
          id
          path
          hash
        }
      }
      paginatorInfo {
        hasMorePages
        currentPage
      }
    }
  }
`)

const MobileShows = () => {
  const [search, setSearch] = useState("")

  const [selectedShowId, setSelectedShowId] = useState("")

  const [debouncedSearch] = useDebounce(search, DEBOUNCE_TIMER)
  const [open, setOpen] = useState(false)

  const { data, isFetching, isError, hasNextPage, fetchNextPage } = useInfiniteQuery({
    queryKey: ["tv-shows", debouncedSearch],
    queryFn: async ({ pageParam = 1 }: { pageParam: number }) =>
      graphqlClient.request(TV_SHOWS, {
        first: MOBILE_FETCH_NUMBER,
        page: pageParam,
        keyword: debouncedSearch,
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage: TvShowsQuery) => {
      return lastPage.getTvShows?.paginatorInfo?.hasMorePages
        ? (lastPage.getTvShows.paginatorInfo.currentPage + 1)
        : undefined
    },
  })

  const handleOpenChange = (open: boolean) => {
    setOpen(open)
  }

  const handleSelectedShow = (id: string) => {
    setSelectedShowId(id)
    setOpen(true)
  }

  return (
    <>
      <div className="grid grid-cols-2 gap-4">
        <div className="col-span-2 w-full">
          <Input
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            placeholder="Search"
            leftSection={<SearchIcon />}
          />
        </div>
        {isError && <MobileError className="col-span-2" />}
        {!isError &&
          data?.pages?.flatMap((page) =>
            page?.getTvShows?.data?.map((tvShow) => {
              return (
                <div key={tvShow.id} className="col-span-1">
                  <AspectRatio ratio={3 / 4}>
                    <Button
                      onClick={() => handleSelectedShow(tvShow.id)}
                      key={tvShow.id}
                      className="flex size-full flex-col p-0"
                      variant="ghost"
                    >
                      <BlurhashImage
                        className="size-full object-cover"
                        alt={tvShow?.title}
                        src={`${baseUrl}/image/medium/${tvShow?.imagePortrait?.path}`}
                        blurhash={tvShow?.imagePortrait?.hash || ""}
                      />
                    </Button>
                  </AspectRatio>
                  <h2 className="px-1 text-center text-sm">{tvShow?.title}</h2>
                </div>
              )
            })
          )}
        {isFetching && <MobileLoading className="col-span-1" />}
        {hasNextPage && (
          <div className="col-span-2 mx-auto">
            <Button
              isLoading={isFetching}
              onClick={() => void fetchNextPage()}
              className="col-span-2"
            >
              Load more
            </Button>
          </div>
        )}
      </div>
      <TVDetailDialog
        open={open}
        handleOpenChange={handleOpenChange}
        showId={selectedShowId}
      />
    </>
  )
}

export default MobileShows
