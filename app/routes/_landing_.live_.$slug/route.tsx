import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>oa<PERSON> } from "@/components/common"
import { LeftIcon, MaximizeIcon } from "@/components/icons"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { graphql } from "@/gql"
import { WatchableContentTypes } from "@/gql/graphql"
import { GENERATE_CONTENT_URL } from "@/lib/graphql/mutation"
import { usePlayVideo } from "@/lib/hooks"
import { baseUrl, getTokenFromUrl, graphqlClient } from "@/lib/utils"
import MuxPlayer from "@mux/mux-player-react/lazy"
import { Link, useLoaderData, useParams } from "react-router"
import {
  MediaControlBar,
  MediaController,
  MediaLiveButton,
  MediaLoadingIndicator,
  MediaMuteButton,
  MediaPlayButton,
  MediaVolumeRange,
} from "media-chrome/react"

import {
  MediaRenditionMenu,
  MediaSettingsMenu,
  MediaSettingsMenuButton,
  MediaSettingsMenuItem,
} from "media-chrome/react/menu"

import type { Route } from "./+types/route"
import { useQuery } from "@tanstack/react-query"

const LIVE_BY_SLUG = graphql(`
  query LiveBySlug($slug: String!) {
    getLiveBySlug(slug: $slug) {
      id
      title
      cdn_playback_id
      cdn_content_status
      description_en
      imageLandscape {
        path
      }
    }
  }
`)

export async function loader({ params }: Route.LoaderArgs) {
  const response = await graphqlClient.request(LIVE_BY_SLUG, {
    slug: params.slug,
  })

  const live = response.getLiveBySlug
  
  if (live && params.slug) {
    return { live }
  } else {
    throw new Error("404. Not Found")
  }
}

export const meta = ({ data, location }: Route.MetaArgs) => {
  return [
    { title: `Enila - ${data?.live?.title}` },
    { name: "description", content: `${data?.live?.description_en}` },
    {
      property: "og:title",
      content: `Enila - ${data?.live?.title}`,
    },
    {
      property: "og:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      property: "og:image",
      itemProp: "image",
      content: `${baseUrl}/image/small/${data?.live?.imageLandscape?.path}`,
    },
    {
      property: "og:image:height",
      content: "600",
    },
    {
      property: "og:image:width",
      content: "600",
    },
    {
      property: "robots",
      content: "index,follow",
    },
    {
      property: "og:description",
      content: `${data?.live?.description_en}`,
    },
    {
      property: "twitter:card",
      content: "summary_large_image",
    },
    {
      property: "twitter:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "twitter:title",
      content: `Enila - ${data?.live?.title}`,
    },
    {
      property: "twitter:description",
      content: `${data?.live?.description_en}`,
    },
    {
      property: "twitter:image",
      content: `${baseUrl}/image/small/${data?.live?.imageLandscape?.path}`,
    },
    {
      property: "og:site_name",
      content: "Enila",
    },
  ]
}

const WatchLive = () => {
  const { live } = useLoaderData<typeof loader>()
  const { slug } = useParams()
  const { handlePlay, handleBackButton, handleFullScreen } = usePlayVideo()

  const { data, isLoading, isError } = useQuery({
    queryKey: ["live-by-slug", slug],
    queryFn: async () =>
      graphqlClient.request(LIVE_BY_SLUG, {
        slug: slug!,
      }),
    enabled: !!slug,
  })

  const {
    data: generateContentUrl,
    isLoading: generating,
    isError: generateContentUrlError,
  } = useQuery({
    queryKey: ["generate-content-live-by-id", live?.id],
    queryFn: async () => {
      return graphqlClient.request(GENERATE_CONTENT_URL, {
        id: live?.id,
        contentType: WatchableContentTypes.Live,
      })
    },
    enabled: !!live?.id,
  })

  if (generating || isLoading) {
    return <PageLoader />
  }

  if (generateContentUrlError || isError) {
    return <PageError />
  }

  const contentStatus = data?.getLiveBySlug?.cdn_content_status

  if (contentStatus === "ended") {
    return (
      <div className="relative flex h-screen flex-col items-center justify-center overflow-hidden">
        <h2 className="text-lg">Live stream has ended.</h2>
        <Link to="/">Go home</Link>
      </div>
    )
  }

  if (contentStatus !== "active") {
    return (
      <div className="relative flex h-screen flex-col items-center justify-center overflow-hidden">
        <h2 className="text-lg">{"Live stream hasn't started yet. Please come back later."}</h2>
        <Link to="/">Go home</Link>
      </div>
    )
  }

  const generatedContent = generateContentUrl?.generateContentUrl
  const playbackToken = getTokenFromUrl(generateContentUrl?.generateContentUrl?.url || "")

  return (
    <div className="relative h-screen overflow-hidden">
      {generatedContent ? (
        <MediaController className="size-full">
          <Button
            onClick={() => {
              handleBackButton().catch(console.error)
            }}
            variant="link"
            className="absolute left-4 top-4 z-50 size-12 rounded-full bg-black/30 hover:bg-black/50"
            type="button"
          >
            <LeftIcon className="scale-150" />
          </Button>
          <MuxPlayer
            // @ts-expect-error Muxplayer doesn't allow slot media but it works
            slot="media"
            streamType="live"
            autoPlay="any"
            className="mux-player-no-controls size-full"
            playbackId={data?.getLiveBySlug?.cdn_playback_id || ""}
            tokens={{
              playback: playbackToken || "",
            }}
            onLoadedData={handlePlay}
          />
          <MediaLoadingIndicator
            suppressHydrationWarning
            noautohide
            className="z-40"
            slot="centered-chrome"
            style={{ "--media-loading-indicator-icon-height": "200px" }}
          ></MediaLoadingIndicator>
          <MediaSettingsMenu className="z-50" hidden="true" anchor="auto">
            <MediaSettingsMenuItem>
              Quality
              <MediaRenditionMenu slot="submenu" hidden>
                <div slot="title">Quality</div>
              </MediaRenditionMenu>
            </MediaSettingsMenuItem>
          </MediaSettingsMenu>
          <MediaControlBar className="z-50">
            <MediaPlayButton></MediaPlayButton>
            <MediaLiveButton></MediaLiveButton>
            <MediaMuteButton></MediaMuteButton>
            <MediaVolumeRange />
            <div className="w-full" />
            <MediaSettingsMenuButton></MediaSettingsMenuButton>
            <Button
              className="h-full rounded-none bg-[#14141E]/70"
              variant="link"
              onClick={() => {
                handleFullScreen().catch(console.error)
              }}
            >
              <MaximizeIcon />
            </Button>
          </MediaControlBar>
        </MediaController>
      ) : null}
    </div>
  )
}

export default WatchLive
