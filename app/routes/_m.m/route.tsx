import { graphql } from "@/gql"
import { baseUrl, graphqlClient } from "@/lib/utils"
import MovieDetailDialog from "./movie-detail-dialog"
import { useState } from "react"
import type { Movie , MoviesQuery } from "@/gql/graphql"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { useDebounce } from "use-debounce"
import { DEBOUNCE_TIMER, MOBILE_FETCH_NUMBER } from "@/lib/utils/constants"
import { MobileError, MobileLoading } from "@/components/common"
import { SearchIcon } from "@/components/icons"
import { useInfiniteQuery } from "@tanstack/react-query"

const MOVIES = graphql(`
  query Movies($first: Int, $page: Int, $keyword: String) {
    getMovies(first: $first, page: $page, keyword: $keyword) {
      data {
        id
        title
        description_en
        description_mz
        age_label
        duration
        production_year
        is_rented
        for_subscriber
        price
        is_free
        imagePortrait {
          id
          path
          hash
        }
        imageLandscape {
          id
          path
          hash
        }
      }
      paginatorInfo {
        hasMorePages
        total
        currentPage
      }
    }
  }
`)

const MobileHome = () => {
  const [search, setSearch] = useState("")

  const [debouncedSearch] = useDebounce(search, DEBOUNCE_TIMER)

  const [open, setOpen] = useState(false)
  const [selectedMovie, setSelectedMovie] = useState<Partial<Movie>>({})

  const { data, isFetching, isError, hasNextPage, fetchNextPage } = useInfiniteQuery({
    queryKey: ["movies", debouncedSearch],
    queryFn: async ({ pageParam }: { pageParam: number }) =>
      graphqlClient.request(MOVIES, {
        first: MOBILE_FETCH_NUMBER,
        page: pageParam,
        keyword: debouncedSearch,
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage: MoviesQuery) => {
      return lastPage.getMovies?.paginatorInfo?.hasMorePages
        ? (lastPage.getMovies.paginatorInfo.currentPage + 1)
        : undefined
    },
  })

  const handleOpenChange = (open: boolean) => {
    setOpen(open)
  }

  const handleSelectedMovie = (movie: Partial<Movie>) => {
    setSelectedMovie(movie)
    setOpen(true) // open dialog
  }

  return (
    <>
      <div className="embla grid grid-cols-2 gap-4">
        <div className="embla h-slide-height col-span-2 w-full">
          <Input
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            leftSection={<SearchIcon />}
            placeholder="Search"
          />
        </div>

        {isError && <MobileError className="col-span-2" />}
        {!isError &&
          data?.pages?.flatMap((page) =>
            page?.getMovies?.data?.map((movie) => {
              return (
                <div key={movie.id} className="col-span-1">
                  <AspectRatio ratio={3 / 4}>
                    <Button
                      onClick={() => handleSelectedMovie(movie)}
                      key={movie.id}
                      className="flex size-full flex-col p-0"
                      variant="ghost"
                    >
                      <img
                        className="size-full object-cover"
                        alt={movie?.title}
                        src={`${baseUrl}/image/medium/${movie?.imagePortrait?.path}`}
                      />
                    </Button>
                  </AspectRatio>
                  <h2 className="px-1 text-center text-sm">{movie?.title}</h2>
                </div>
              )
            })
          )}
        {isFetching && <MobileLoading className="col-span-1" />}
        {hasNextPage && (
          <div className="col-span-2 mx-auto">
            <Button
              isLoading={isFetching}
              onClick={() => void fetchNextPage()}
              className="col-span-2"
            >
              Load more
            </Button>
          </div>
        )}
      </div>
      <MovieDetailDialog
        open={open}
        handleOpenChange={handleOpenChange}
        selectedMovie={selectedMovie}
      />
    </>
  )
}

export default MobileHome
