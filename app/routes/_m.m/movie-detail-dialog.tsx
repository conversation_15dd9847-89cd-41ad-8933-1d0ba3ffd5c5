import { BlurhashImage } from "@/components/common"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { Button, buttonVariants } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader } from "@/components/ui/dialog"
import type { Movie } from "@/gql/graphql"
import { RENT_MOVIE } from "@/lib/graphql/mutation"
import { useRazorpay, useUser } from "@/lib/hooks"
import { baseUrl, formatDuration, graphqlClient } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"
import { DialogDescription } from "@radix-ui/react-dialog"
import { useMutation } from "@tanstack/react-query"
import { Link, useNavigate } from "react-router"

interface Props {
  open: boolean
  handleOpenChange: (open: boolean) => void
  selectedMovie: Partial<Movie>
}

const MovieDetailDialog = ({
  open,
  handleOpenChange,
  selectedMovie,
}: Props) => {
  const navigate = useNavigate()
  const { handleRazorpay } = useRazorpay()
  const { data: user } = useUser()

  const rentMovie = useMutation({
    mutationFn: async () =>
      await graphqlClient.request(RENT_MOVIE, {
        id: selectedMovie.id!,
        redirectURL: window.location.href,
      }),
    onSuccess: (data) => {
      if (data?.rentMovie?.__typename === "PhonePePaymentResponse") {
        navigate(data.rentMovie.goto_url)
      }
      if (data?.rentMovie?.__typename === "RazorPayPaymentResponse") {
        handleRazorpay({
          price: selectedMovie.price || 0,
          orderId: data?.rentMovie.order_id,
          user: user?.getMe,
          onLoadCallback: () => handleOpenChange(false),
          redirectUrl: window.location.href,
        })
      }
    },
  })

  const handleRentMovie = () => {
    rentMovie.mutate()
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent>
        <AspectRatio ratio={16 / 9}>
          <BlurhashImage
            className="size-full object-cover"
            alt={selectedMovie?.title || ""}
            src={`${baseUrl}/image/medium/${selectedMovie?.imageLandscape?.path}`}
            blurhash={selectedMovie?.imageLandscape?.hash || ""}
          />
          {/* <img */}
          {/*   className="size-full object-cover" */}
          {/*   alt={selectedMovie?.title} */}
          {/*   src={`${baseUrl}/image/medium/${selectedMovie?.imageLandscape?.path}`} */}
          {/* /> */}
        </AspectRatio>
        <DialogHeader className="font-bold">
          {selectedMovie?.title}
        </DialogHeader>
        <div className="flex justify-center text-xs font-semibold">
          <span className="border-r-2 border-white pr-2">
            {selectedMovie?.production_year}
          </span>
          {selectedMovie?.duration ? (
            <span className="border-r-2 border-white px-2">
              {formatDuration(selectedMovie?.duration)}
            </span>
          ) : null}
          <span className="px-2">{selectedMovie?.age_label}</span>
        </div>
        <DialogDescription className="text-sm">
          Purchase content can be watched with EN ILA mobile app available on
          the App store or Play store or a desktop browser.
          <br />
          Purchase will be available for 72 hours from the time of purchase.
          Refund is not available for any item purchased. In case of failed
          payments or other issues, please contact our customer support.
          <br />
          For more information refer to our{" "}
          <a href="/refund-policy" className="text-theme-blue">
            refund policy
          </a>
        </DialogDescription>
        {/* Same logic as movie-action component */}
        {selectedMovie?.for_subscriber ? (
          <>
            {selectedMovie?.is_rented ? (
              <div
                className={cn(
                  buttonVariants({ variant: "secondary" }),
                  "w-full"
                )}
              >
                Rented
              </div>
            ) : (
              <Link
                className={cn(buttonVariants({ variant: "default" }), "w-full")}
                to={"/m/subscription"}
              >
                Subscribe to Play
              </Link>
            )}
          </>
        ) : (
          <>
            {selectedMovie?.is_free || selectedMovie?.is_rented ? (
              <div
                className={cn(
                  buttonVariants({ variant: "secondary" }),
                  "w-full"
                )}
              >
                {selectedMovie?.is_free ? "Free" : "Rented"}
              </div>
            ) : (
              <Button isLoading={rentMovie.isPending} onClick={handleRentMovie}>
                Rent
              </Button>
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}

export default MovieDetailDialog
