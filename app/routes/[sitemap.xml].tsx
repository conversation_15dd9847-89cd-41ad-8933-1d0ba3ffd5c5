import { baseWebUrl } from "@/lib/utils"

export const loader = () => {
  // These two functions get a list of all the posts and projects, using prisma
  // I will write more blog posts on prisma in the future and explain how it's used

  const content = `
      <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      <url>
      <loc>${baseWebUrl}</loc>
      <lastmod></lastmod>
      </url>
      <url>
      <loc>${baseWebUrl}/login</loc>
      </url>
      <url>
      <loc>${baseWebUrl}/subscription</loc>
      </url>
      <url>
      <loc>${baseWebUrl}/terms-and-conditions</loc>
      </url>
      <url>
      <loc>${baseWebUrl}/privacy-policy</loc>
      </url>
      <url>
      <loc>${baseWebUrl}/refund-policy</loc>
      </url>
      </urlset>
      `

  return new Response(content, {
    status: 200,
    headers: {
      "Content-Type": "application/xml",
      "xml-version": "1.0",
      encoding: "UTF-8",
    },
  })
}
