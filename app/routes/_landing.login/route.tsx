import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { graphqlClient } from "@/lib/utils"
import { useState } from "react"
import {
  type LoginSchemaType,
  type OtpSchemaType,
  loginSchema,
  otpSchema,
} from "@/lib/types"
import { ClientError } from "graphql-request"
import { toast } from "@/components/ui/use-toast"
import { USER_LOGIN } from "@/lib/graphql/mutation"
import { VerifyOtpDialog } from "@/components/common"
import { useMutation } from "@tanstack/react-query"
import type { Route } from "./+types/route"
import { commitSession, destroySession, getSession } from "@/sessions"
import { redirect } from "react-router"

export async function action({ request }: Route.ActionArgs) {
  const session = await getSession(request.headers.get("Cookie"))
  const formData = await request.formData()
  const token = formData.get("token")
  const ref = new URL(request.url).searchParams.get("ref")

  if (!token || typeof token !== "string") {
    return redirect("/login", {
      headers: {
        "Set-Cookie": await destroySession(session),
      },
    })
  }

  session.set("token", token)

  if (ref) {
    return redirect(ref, {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    })
  }
  return redirect("/", {
    headers: {
      "Set-Cookie": await commitSession(session),
    },
  })
}

export default function Login() {
  const [openOtp, setOpenOtp] = useState(false)
  const loginForm = useForm<LoginSchemaType>({
    resolver: zodResolver(loginSchema),
  })

  const otpForm = useForm<OtpSchemaType>({
    resolver: zodResolver(otpSchema),
  })

  const userLogin = useMutation({
    mutationFn: async (variables: LoginSchemaType) => {
      if (variables.credentials.includes("@")) {
        const data = await graphqlClient.request(USER_LOGIN, {
          email: variables.credentials,
        })
        return data
      } else {
        const data = await graphqlClient.request(USER_LOGIN, {
          mobileNumber: variables.credentials,
        })
        return data
      }
    },
    onSuccess: (data) => {
      if (data.userLogin?.otp_id) {
        setOpenOtp(true)
        otpForm.setValue("otpId", data.userLogin.otp_id)
      }
    },
    onError: (error) => {
      if (error instanceof ClientError) {
        toast({
          description:
            error?.response?.errors?.[0]?.message || "Error validating otp",
        })
      }
    },
  })

  const handleLogin = (data: LoginSchemaType) => {
    userLogin.mutate(data)
  }

  const handleDialogChange = (state: boolean) => {
    setOpenOtp(state)
  }

  return (
    <>
      <section className="flex h-full min-h-screen flex-col items-center justify-center">
        <div className="my-4 text-center text-2xl font-bold">ENILA</div>
        <form
          onSubmit={loginForm.handleSubmit(handleLogin)}
          className="flex w-1/4 flex-col"
        >
          <h1 className="my-2 font-bold">Login</h1>
          <Label className="my-3">Mobile number / Email</Label>
          <Input {...loginForm.register("credentials")} className="mb-3" />
          <Button
            type="submit"
            className="w-24"
            isLoading={userLogin.isPending}
          >
            Login
          </Button>
        </form>
      </section>
      <VerifyOtpDialog
        open={openOtp}
        handleDialogChange={handleDialogChange}
        form={otpForm}
        resendOtp={() => handleLogin(loginForm.getValues())}
      />
    </>
  )
}
