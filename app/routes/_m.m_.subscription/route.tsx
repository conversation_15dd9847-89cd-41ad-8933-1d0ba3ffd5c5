import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Rupee } from "@/components/common"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { SUBSCRIBE_TO_PLAN } from "@/lib/graphql/mutation"
import { SUBSCRIPTION_PLANS } from "@/lib/graphql/queries"
import { useRazorpay, useUser } from "@/lib/hooks"
import { baseWebUrl, graphqlClient } from "@/lib/utils"
import { zodResolver } from "@hookform/resolvers/zod"
import { useSearchParams } from "react-router"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { useMutation, useQuery } from "@tanstack/react-query"

const schema = z.object({
  planId: z.string().min(1, "Please select a plan"),
})

type FormSchemaType = z.infer<typeof schema>

const MobileSubscription = () => {
  const { data, isLoading, isError } = useQuery({
    queryKey: ["subscription-plans"],
    queryFn: async () => graphqlClient.request(SUBSCRIPTION_PLANS),
  })

  const { data: user } = useUser()

  const [searchParams] = useSearchParams()

  const { handleRazorpay } = useRazorpay()

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
    defaultValues: {
      planId: "",
    },
  })

  const subscribeToPlan = useMutation({
    mutationFn: async (data: FormSchemaType) => {
      return await graphqlClient.request(SUBSCRIBE_TO_PLAN, {
        redirectUrl: baseWebUrl + "/m/payment-success",
        planId: data.planId,
      })
    },
    onSuccess: (data) => {
      if (data.subscribeToPlan?.__typename === "PhonePePaymentResponse") {
        window.location.href = baseWebUrl + data.subscribeToPlan.goto_url
      } else if (
        data.subscribeToPlan?.__typename === "RazorPayPaymentResponse"
      ) {
        const price = data.subscribeToPlan.price
        const orderId = data.subscribeToPlan.order_id
        const ref = searchParams.get("ref")
        handleRazorpay({
          orderId: orderId,
          price: price,
          user: user?.getMe,
          gotoUrl: ref,
        })
      }
    },
    onError: (error) => {
      console.log("mutation failed", error)
    },
  })

  const handleSubscription = (data: FormSchemaType) => {
    subscribeToPlan.mutate(data)
  }

  if (isLoading) {
    return <PageLoader />
  }

  if (isError) {
    return <PageError />
  }

  return (
    <>
      <div className="flex flex-col">
        <section className="space-y-4">
          <p>Disclaimer:</p>
          <p>
            Subscriber exclusive content can be watched with EN ILA mobile app
            available on the App store or Play store or a desktop browser.
          </p>
          <p>
            Refund is not available for any item purchased. In case of failed
            payments or other issues, please contact our customer support.
          </p>
          <p>For more information, refer to our refund policy</p>
        </section>
        <section className="my-4">
          <h1 className="font-bold">Select subscription plan</h1>
        </section>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubscription)}
            className="flex flex-col space-y-4"
          >
            <FormField
              control={form.control}
              name="planId"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-2"
                    >
                      {data?.subscriptionPlans?.map((item) => {
                        return (
                          item && (
                            <FormItem
                              key={item?.id}
                              className={
                                "flex h-full items-center space-x-3 space-y-0 rounded-lg border border-white p-2"
                              }
                            >
                              <FormControl>
                                <RadioGroupItem value={item?.id} />
                              </FormControl>
                              <FormLabel className="flex w-full items-center justify-between">
                                <div className="flex flex-col">
                                  <span>{item?.name}</span>
                                  <span>
                                    {item.duration > 1
                                      ? `${item.duration} Months`
                                      : `${item.duration} Month`}
                                  </span>
                                </div>
                                <div>
                                  <Rupee amount={item?.price} />
                                </div>
                              </FormLabel>
                            </FormItem>
                          )
                        )
                      })}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button isLoading={subscribeToPlan.isPending} type="submit">
              Continue
            </Button>
          </form>
        </Form>
      </div>
    </>
  )
}

export default MobileSubscription
