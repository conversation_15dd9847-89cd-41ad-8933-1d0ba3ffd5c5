import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, RentEpisodeDialog } from "@/components/common"
import { LeftIcon, MaximizeIcon } from "@/components/icons"
import { Button } from "@/components/ui/button"
import { graphql } from "@/gql"
import { WatchDurationContentType, WatchableContentTypes } from "@/gql/graphql"
import { GENERATE_CONTENT_URL, UPSERT_WATCHED_DURATION } from "@/lib/graphql/mutation"
import type { ContinueWatchingType } from "@/lib/types"
import { baseUrl, getTokenFromUrl, graphqlClient } from "@/lib/utils"
import { useLoaderData} from "react-router"
import { useEffect, useRef } from "react"
import EpisodePlaylist from "./episode-playlist"
import MuxPlayer from "@mux/mux-player-react/lazy"
import {
  MediaControlBar,
  MediaController,
  MediaLoadingIndicator,
  MediaMuteButton,
  MediaPlayButton,
  MediaSeekBackwardButton,
  MediaSeekForwardButton,
  MediaTimeDisplay,
  MediaTimeRange,
  MediaVolumeRange,
} from "media-chrome/react"

import {
  MediaCaptionsMenu,
  MediaCaptionsMenuButton,
  MediaSettingsMenu,
  MediaSettingsMenuButton,
  MediaSettingsMenuItem,
  MediaPlaybackRateMenu,
  MediaRenditionMenu,
} from "media-chrome/react/menu"

import { usePlayVideo } from "@/lib/hooks"
import MuxPlayerElement from "@mux/mux-player"
import { CONTINUE_WATCHING_INTERVAL } from "@/lib/utils/constants"
import type { Route } from "./+types/route"
import { useMutation, useQuery } from "@tanstack/react-query"
import { getSession } from "@/sessions"

const TV_SHOW_EPISODE_BY_ID = graphql(`
  query TvEpisodeById($id: ID!) {
    tvShowEpisodeById(id: $id) {
      id
      title
      tv_show_id
      cdn_playback_id
      description_en
      continueWatching {
        watched_duration
      }
      imageLandscape {
        path
      }
      season {
        title
        episodes {
          ...EpisodePlaylistFragment
          cdn_playback_id
        }
      }
      tvShow {
        title
        price_per_episode
      }
    }
  }
`)

export async function loader({ request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get("Cookie"))

  const token = session.get("token")

  const url = new URL(request.url)
  const episodeId = url.searchParams.get("eid")
  if (episodeId) {
    const response = await graphqlClient.request(
      TV_SHOW_EPISODE_BY_ID,
      {
        id: episodeId,
      },
      {
        Authorization: `Bearer ${token}`,
      }
    )

    const episode = response.tvShowEpisodeById

    return { episode, episodeId }
  } else {
    // replaces previous throw json("Not Found", { status: 404 })
    throw new Error("404. Not Found")
  }
}

export const meta = ({ data, location }: Route.MetaArgs) => {
  return [
    {
      title: `Enila - ${data?.episode?.tvShow?.title} - ${data?.episode?.season?.title} - ${data?.episode?.title}`,
    },
    {
      property: "og:title",
      content: `Enila - ${data?.episode?.tvShow?.title} - ${data?.episode?.season?.title} - ${data?.episode?.title}`,
    },
    {
      property: "og:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      property: "og:image",
      itemProp: "image",
      content: data?.episode?.imageLandscape?.path
        ? `${baseUrl}/image/small/${data?.episode?.imageLandscape?.path}`
        : "",
    },
    {
      property: "og:image:height",
      content: "600",
    },
    {
      property: "og:image:width",
      content: "600",
    },
    {
      property: "robots",
      content: "index,follow",
    },
    {
      property: "og:description",
      content: `${data?.episode?.description_en}`,
    },
    {
      property: "twitter:card",
      content: "summary_large_image",
    },
    {
      property: "twitter:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "twitter:title",
      content: `Enila - ${data?.episode?.tvShow?.title} - ${data?.episode?.season?.title} - ${data?.episode?.title}`,
    },
    {
      property: "twitter:description",
      content: `${data?.episode?.description_en}`,
    },
    {
      property: "twitter:image",
      content: data?.episode?.imageLandscape?.path
        ? `${baseUrl}/image/small/${data?.episode?.imageLandscape?.path}`
        : "",
    },
    {
      property: "og:site_name",
      content: "Enila",
    },
  ]
}

const WatchShow = () => {
  const { episode } = useLoaderData<typeof loader>()

  const videoRef = useRef<MuxPlayerElement | null>(null)

  const { isLoading, handlePlay, handleBackButton, handleFullScreen } = usePlayVideo()

  const {
    data,
    isLoading: generating,
    isError,
  } = useQuery({
    queryKey: ["generate-content-show-by-id", episode!.id],
    queryFn: async () => {
      return graphqlClient.request(GENERATE_CONTENT_URL, {
        id: episode!.id,
        contentType: WatchableContentTypes.TvShowEpisode,
      })
    },
    enabled: !!episode?.id,
  })

  const handleContinueWatching = useMutation({
    mutationFn: async (variables: ContinueWatchingType) =>
      await graphqlClient.request(UPSERT_WATCHED_DURATION, {
        contentId: variables.id,
        contentType: variables.contentType,
        duration: variables.duration,
        totalDuration: variables.totalDuration,
      }),
  })

  useEffect(() => {
    const progressInterval = setInterval(() => {
      const currentTime = videoRef?.current?.currentTime
      const totalDuration = videoRef?.current?.duration

      const isPlaying = !videoRef?.current?.paused

      if (isPlaying && episode?.id) {
        handleContinueWatching.mutate({
          id: episode.id,
          contentType: WatchDurationContentType.TvShowEpisode,
          duration: Math.trunc(currentTime || 0),
          totalDuration: Math.ceil(totalDuration || 0),
        })
      }
    }, CONTINUE_WATCHING_INTERVAL)

    return () => clearInterval(progressInterval)
  }, [handleContinueWatching, episode?.id])

  if (generating) {
    return <PageLoader />
  }

  if (isError) {
    return <PageError />
  }

  const generatedContent = data?.generateContentUrl

  const playbackToken = getTokenFromUrl(generatedContent?.url || "")

  const storyboardToken = getTokenFromUrl(generatedContent?.story_board_url || "")

  return (
    <>
      <div className="relative h-screen overflow-hidden">
        {episode && generatedContent ? (
          <MediaController className="size-full">
            <Button
              onClick={() => {
                handleBackButton().catch(console.error)
              }}
              variant="link"
              className="absolute left-4 top-4 z-50 size-12 rounded-full bg-black/30 hover:bg-black/50"
              type="button"
            >
              <LeftIcon className="scale-150" />
            </Button>
            {episode.season?.episodes && (
              <EpisodePlaylist
                // @ts-expect-error jsonified object doesn't match the fragment but data is correct
                data={episode.season?.episodes}
                episodePrice={episode.tvShow?.price_per_episode}
              />
            )}
            <MuxPlayer
              // @ts-expect-error Muxplayer doesn't allow slot media but it works
              slot="media"
              playbackId={episode?.cdn_playback_id || ""}
              ref={videoRef}
              tokens={{
                playback: playbackToken || "",
                storyboard: storyboardToken || "",
              }}
              currentTime={episode?.continueWatching?.watched_duration}
              poster={
                episode?.imageLandscape?.path
                  ? `${baseUrl}/image/medium/${episode?.imageLandscape.path}`
                  : ""
              }
              autoPlay="any"
              streamType="on-demand"
              className="mux-player-no-controls size-full"
              onLoadedData={handlePlay}
            ></MuxPlayer>
            {isLoading ? (
              <MediaLoadingIndicator
                suppressHydrationWarning
                noautohide
                className="z-40"
                slot="centered-chrome"
                style={{ "--media-loading-indicator-icon-height": "200px" }}
              ></MediaLoadingIndicator>
            ) : (
              <MediaPlayButton
                style={{
                  "--media-button-icon-height": "100px",
                  "--media-button-icon-width": "100px",
                }}
                className="z-40 size-full bg-transparent focus:outline-none"
                slot="centered-chrome"
              ></MediaPlayButton>
            )}
            <MediaSettingsMenu className="z-50" hidden={"true"} anchor="auto">
              <MediaSettingsMenuItem>
                Speed
                <MediaPlaybackRateMenu slot="submenu" hidden>
                  <div slot="title">Speed</div>
                </MediaPlaybackRateMenu>
              </MediaSettingsMenuItem>
              <MediaSettingsMenuItem>
                Quality
                <MediaRenditionMenu slot="submenu" hidden>
                  <div slot="title">Quality</div>
                </MediaRenditionMenu>
              </MediaSettingsMenuItem>
            </MediaSettingsMenu>
            <MediaCaptionsMenu className="z-50" hidden anchor="auto"></MediaCaptionsMenu>
            <MediaControlBar className="z-50">
              <MediaPlayButton></MediaPlayButton>
              <MediaSeekBackwardButton></MediaSeekBackwardButton>
              <MediaSeekForwardButton></MediaSeekForwardButton>
              <MediaMuteButton></MediaMuteButton>
              <MediaVolumeRange />
              <MediaTimeRange></MediaTimeRange>
              <MediaTimeDisplay showDuration></MediaTimeDisplay>
              <MediaCaptionsMenuButton></MediaCaptionsMenuButton>
              <MediaSettingsMenuButton></MediaSettingsMenuButton>
              <Button
                className="h-full rounded-none bg-[#14141E]/70"
                variant="link"
                onClick={() => {
                  handleFullScreen().catch(console.error)
                }}
              >
                <MaximizeIcon />
              </Button>
            </MediaControlBar>
          </MediaController>
        ) : null}
      </div>
      <RentEpisodeDialog />
    </>
  )
}

export default WatchShow
