import { ListIcon } from "@/components/icons"
import { But<PERSON>, buttonVariants } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { type FragmentType, graphql, useFragment } from "@/gql"
import { useLanguage, useRentEpisodeDialogStore } from "@/lib/store"
import { baseUrl } from "@/lib/utils"
import { useNavigate } from "react-router"
import { type EpisodePlaylistFragmentFragment } from "@/gql/graphql"
import { Rupee } from "@/components/common"

const EPISODE_PLAYLIST_FRAGMENT = graphql(`
  fragment EpisodePlaylistFragment on TVShowEpisodes {
    id
    title
    cdn_playback_id
    description_en
    description_mz
    is_rented
    is_free
    for_subscriber
    continueWatching {
      watched_duration
    }
    imageLandscape {
      id
      hash
      path
    }
  }
`)

interface Props {
  data: Array<FragmentType<typeof EPISODE_PLAYLIST_FRAGMENT>>
  episodePrice?: number
}

const EpisodePlaylist = ({ data, episodePrice }: Props) => {
  const episodes = useFragment(EPISODE_PLAYLIST_FRAGMENT, data)
  const navigate = useNavigate()

  const { openRentEpisodeDialog } = useRentEpisodeDialogStore()

  const handleEpisodeClick = (episode: EpisodePlaylistFragmentFragment) => {
    if (episode.is_rented || episode.is_free) {
      navigate(
        {
          search: `?eid=${episode.id}`,
        },
        {
          replace: true,
        }
      )
    } else if (episode.for_subscriber) {
      navigate({
        pathname: "/subscription",
        search: `?ref=${window.location.pathname}${window.location.search}`,
      })
    } else {
      openRentEpisodeDialog(episode.id, episodePrice!)
    }
  }

  const { language } = useLanguage()
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="link"
          className="absolute right-4 top-4 z-50 size-12 rounded-full bg-black/30 hover:bg-black/50"
        >
          <ListIcon className="scale-150" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-[28rem]">
        <DropdownMenuGroup>
          {episodes.map((episode) => (
            <DropdownMenuItem key={episode.id} asChild>
              <Button
                variant="ghost"
                onClick={() => handleEpisodeClick(episode)}
                // to={`?eid=${episode.id}`}
                className="size-full p-1 text-start"
                // replace={true}
              >
                <div className="grid grid-cols-12 gap-x-4">
                  <img
                    className="col-span-3"
                    alt={episode.title}
                    src={`${baseUrl}/image/thumbnail/${episode.imageLandscape?.path}`}
                  />
                  {/* <BlurhashImage */}
                  {/*   className="col-span-3 w-full" */}
                  {/*   alt={episode.title} */}
                  {/*   src={`${baseUrl}/image/thumbnail/${episode.imageLandscape?.path}`} */}
                  {/*   blurhash={episode.imageLandscape?.hash || ""} */}
                  {/* /> */}
                  <div className="col-span-6 flex flex-col">
                    <div>{episode.title}</div>
                    <div className="line-clamp-2 whitespace-pre-wrap text-sm">
                      {language === "en"
                        ? episode.description_en
                        : episode.description_mz}
                    </div>
                  </div>
                  <div className="col-span-3 my-auto">
                    <EpisodeActionButtons
                      episodePrice={episodePrice}
                      episode={episode}
                    />
                  </div>
                </div>
              </Button>
            </DropdownMenuItem>
          ))}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

interface EpisodeActionProps {
  episodePrice?: number
  episode: EpisodePlaylistFragmentFragment
}

const EpisodeActionButtons = ({
  episodePrice,
  episode,
}: EpisodeActionProps) => {
  if (episode.is_rented || episode.is_free) {
    return (
      <div
        className={buttonVariants({
          variant: "default",
          className: "w-full",
        })}
      >
        {episode.continueWatching?.watched_duration ? "Resume" : "Play"}
      </div>
    )
  } else if (episode.for_subscriber) {
    return (
      <div
        className={buttonVariants({ variant: "default", className: "w-full" })}
      >
        Subscribe
      </div>
    )
  } else {
    return (
      <div
        className={buttonVariants({
          variant: "default",
          className: "w-full",
        })}
      >
        Rent <Rupee amount={episodePrice!} />
      </div>
    )
  }
}

export default EpisodePlaylist
