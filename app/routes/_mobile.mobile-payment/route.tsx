import { useRazorpay } from "@/lib/hooks"
import { useLoaderData } from "react-router"
import { useEffect, useRef, useState } from "react"

import type { Route } from "../_mobile.mobile-payment/+types/route"

export const loader = ({ request }: Route.LoaderArgs) => {
  const url = new URL(request.url)
  const searchParams = new URLSearchParams(url.search)

  const orderID = searchParams.get("order_id")
  const price = searchParams.get("price")
  const mobileNumber = searchParams.get("mobile_number")
  const callbackUrl = searchParams.get("callback_url")
  const email = searchParams.get("email")

  if (orderID && price && callbackUrl && (mobileNumber || email)) {
    return {
      orderID,
      price: parseInt(price),
      mobileNumber,
      email,
      callbackUrl,
    }
  } else {
    throw new Error("Invalid keys provided.")
  }
}

export default function MobilePayment() {
  const { orderID, price, mobileNumber, callbackUrl, email } =
    useLoaderData<typeof loader>()

  const effectRef = useRef(false)
  const [loading, setLoading] = useState(false)

  const { handleRazorpay } = useRazorpay()

  useEffect(() => {
    if (!effectRef.current) {
      effectRef.current = true
      handleRazorpay({
        orderId: orderID,
        price,
        contact: mobileNumber || "",
        email: email || "",
        backdropFn: () => setLoading(!loading),
        callbackUrl: callbackUrl,
      })
    }
  }, [])

  if (loading) {
    return <div>Loading...</div>
  }
}
