import { Footer } from "@/components/common"
import { cn } from "@/lib/utils/cn"
import { Link, Outlet } from "react-router"
import { useEffect, useState } from "react"

import type { Route } from "../_mobile/+types/route"

export const meta = ({ location }: Route.MetaArgs) => {
  return [
    { title: "Enila" },
    {
      name: "description",
      content:
        "Enila is a user friendly streaming platform made with love in Aizawl, Mizoram",
    },
    {
      property: "og:title",
      content: `Enila`,
    },
    {
      property: "og:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      property: "og:image",
      itemProp: "image",
      content: "/enila_logo.png",
    },
    {
      property: "og:image:height",
      content: "600",
    },
    {
      property: "og:image:width",
      content: "600",
    },
    {
      property: "robots",
      content: "index,follow",
    },
    {
      property: "og:description",
      content:
        "Enila is a user friendly streaming platform made with love in Aizawl, Mizoram",
    },
    {
      property: "twitter:card",
      content: "summary_large_image",
    },
    {
      property: "twitter:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "twitter:title",
      content: "Enila",
    },
    {
      property: "twitter:description",
      content:
        "Enila is a user friendly streaming platform made with love in Aizawl, Mizoram",
    },
    {
      property: "twitter:image",
      content: "/enila_logo.png",
    },
    {
      property: "og:site_name",
      content: "Enila",
    },
  ]
}

const MobileLayouts = () => {
  const [isScrolled, setIsScrolled] = useState(false)

  const handleScroll = () => {
    if (window.scrollY > 0) {
      setIsScrolled(true)
    } else {
      setIsScrolled(false)
    }
  }

  useEffect(() => {
    window.addEventListener("scroll", handleScroll)
    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [])

  return (
    <div className="flex h-full min-h-screen flex-col">
      <header>
        <nav
          className={cn(
            "fixed z-50 flex h-[10vh] w-full items-center justify-center px-2 transition-colors duration-1000 ease-out",
            {
              "bg-black": isScrolled,
              "bg-transparent": !isScrolled,
            }
          )}
        >
          <Link
            to="/"
            className="flex items-center justify-center gap-x-2 text-4xl font-bold text-theme-yellow"
          >
            <img
              alt="enila logo"
              className="mt-1 size-12"
              src="/enila_logo.png"
            />
            ENILA
          </Link>
        </nav>
      </header>
      {/* TODO: Add padding here maybe */}
      <main className="grow">
        <Outlet />
      </main>
      <Footer />
    </div>
  )
}

export default MobileLayouts
