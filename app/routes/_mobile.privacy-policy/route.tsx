export const meta = () => {
  return [{ title: "Enila - Privacy Policy" }]
}

const PrivacyPolicy = () => {
  return (
    <div className="mx-auto flex h-full min-h-screen max-w-4xl flex-col px-4 pt-[10vh] lg:px-0">
      <h1 className="text-4xl font-bold">Privacy Policy</h1>
      <p className="mt-4">
        At Enila, we are committed to protecting the privacy and security of our
        users' personal information. This Privacy Policy outlines how we
        collect, use, disclose, and protect the information collected through
        the Enila OTT (Over-the-Top) Streaming Service. By accessing or using
        our service, you consent to the practices described in this Privacy
        Policy. We encourage you to read this Privacy Policy carefully to
        understand our practices regarding your personal information. If you
        have any questions or concerns about our Privacy Policy or our data
        practices, please contact us using the information provided at the end
        of this document.
      </p>

      <h2 className="mt-4 font-bold">1. Information Collection:</h2>
      <ol className="list-inside list-latin">
        <li>
          Enila collects personal information from users in connection with the
          provision of the Enila Streaming Service. This may include information
          provided during account registration, payment processing, and
          interactions with customer support.
        </li>
        <li>
          Enila may also collect non-personal information such as device
          information, log data, and cookies for the purpose of improving the
          service and user experience.
        </li>
      </ol>

      <h2 className="mt-4 font-bold">2. Use of Information:</h2>
      <ol className="list-inside list-latin">
        <li>
          Enila may use the information collected for the following purposes:
        </li>
        <ul className="ml-4 list-inside list-disc">
          <li>To provide and maintain the Enila Streaming Service</li>
          <li>To process payments and fulfill subscription orders</li>
          <li>
            To communicate with users about their accounts and the service
          </li>
          <li>
            To personalize user experience and provide tailored content
            recommendations
          </li>
        </ul>
        <li>
          Enila will not sell, rent, or lease users' personal information to
          third parties without consent, except as required by law or to
          facilitate the provision of the service.
        </li>
      </ol>

      <h2 className="mt-4 font-bold">3. Data Security:</h2>
      <ol className="list-inside list-latin">
        <li>
          Enila implements industry-standard security measures to protect users'
          personal information from unauthorized access, disclosure, alteration,
          or destruction.
        </li>
        <li>
          Despite these measures, no data transmission over the internet or
          electronic storage system is guaranteed to be 100% secure, and Enila
          cannot guarantee the absolute security of users' information.
        </li>
      </ol>

      <h2 className="mt-4 font-bold">4. Third-Party Services:</h2>
      <ol className="list-inside list-latin">
        <li>
          The Enila Streaming Service may integrate with third-party services or
          contain links to third-party websites. Enila is not responsible for
          the privacy practices or content of these third parties.
        </li>
      </ol>

      <h2 className="mt-4 font-bold">5. Changes to the Privacy Policy:</h2>
      <p>
        Enila reserves the right to modify or update this Privacy Policy at any
        time. Any changes will be effective immediately upon posting the revised
        Privacy Policy on the Enila website or through the service.
      </p>

      <h2 className="mt-4 font-bold">6. Contact Us:</h2>
      <p className="pb-8">
        If you have any questions or concerns about these Terms and Conditions
        or the Privacy Policy, please contact us at [Contact Information].
      </p>
    </div>
  )
}

export default PrivacyPolicy
