import { BlurhashImage } from "@/components/common"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { type FragmentType, graphql, useFragment } from "@/gql"
import { useLanguage } from "@/lib/store"
import { baseUrl } from "@/lib/utils"
import { Link } from "react-router"

const WATCH_LIST_EPISODE_FRAGMENT = graphql(`
  fragment WatchListEpisodeFragment on TVShowEpisodes {
    id
    title
    description_en
    description_mz
    sub_header
    imageLandscape {
      id
      hash
      path
    }
    tv_show_id
    tv_show_season_id
    season {
      title
    }
    tvShow {
      slug
      title
    }
  }
`)

interface Props {
  data: FragmentType<typeof WATCH_LIST_EPISODE_FRAGMENT>
}

const WatchListEpisode = ({ data }: Props) => {
  const episode = useFragment(WATCH_LIST_EPISODE_FRAGMENT, data)
  const { language } = useLanguage()

  if (!episode.tv_show_id) {
    return (
      <div className="col-span-6 hover:bg-accent">
        <div className="grid grid-cols-12">
          <div className="col-span-4">
            <AspectRatio ratio={16 / 9}>
              <img
                alt="not found"
                className="size-full object-cover"
                src="/not_found.jpg"
              />
            </AspectRatio>
          </div>
          <div className="col-span-8 px-4">
            <p>Content no longer available</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <Link
      className="col-span-6 hover:bg-accent"
      to={`/show/${episode?.tvShow?.slug}?sid=${episode?.tv_show_season_id}`}
    >
      <div className="grid grid-cols-12">
        <div className="col-span-4">
          <AspectRatio ratio={16 / 9}>
            <BlurhashImage
              className="size-full object-cover"
              alt={episode.title}
              src={`${baseUrl}/image/small/${episode?.imageLandscape?.path}`}
              blurhash={episode?.imageLandscape?.hash || ""}
            />
          </AspectRatio>
        </div>
        <div className="col-span-8 px-4">
          <p>
            {episode.tvShow?.title} - {episode.season?.title} - {episode.title}{" "}
            <span className="text-sm text-gray-400">{episode?.sub_header}</span>
          </p>
          <p className="line-clamp-6 whitespace-pre-wrap text-sm">
            {language === "en"
              ? episode.description_en
              : episode.description_mz}
          </p>
        </div>
      </div>
    </Link>
  )
}

export default WatchListEpisode
