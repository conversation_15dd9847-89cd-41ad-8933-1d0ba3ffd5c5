import { graphqlClient } from "@/lib/utils"
import WatchListMovie from "./watch-list-movie"
import { graphql } from "@/gql"
import WatchListSeason from "./watch-list-season"
import WatchListEpisode from "./watch-list-episode"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
} from "@/components/ui/pagination"
import { useSearchParams } from "react-router"
import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils/cn"
import { PageError, PageLoader } from "@/components/common"
import { useQuery } from "@tanstack/react-query"

const MY_WATCH_LIST = graphql(`
  query MyWatchList($page: Int, $first: Int!) {
    myWatchList(first: $first, page: $page) {
      data {
        watch_listable {
          __typename
          ...WatchListMovieFragment
          ...WatchListSeasonFragment
          ...WatchListEpisodeFragment
        }
      }
      paginatorInfo {
        lastPage
        total
      }
    }
  }
`)

const MyWatchList = () => {
  const [searchParams, setSearchParams] = useSearchParams()

  const [page, setPage] = useState(parseInt(searchParams.get("page") || "1"))

  const { data, isLoading, isError } = useQuery({
    queryKey: ["my-watch-list", page],
    queryFn: async () =>
      graphqlClient.request(MY_WATCH_LIST, {
        first: 10,
        page: page,
      }),
    enabled: !!page,
    staleTime: 0,
  })

  const handlePageChange = (page: number) => {
    setPage(page)
    setSearchParams(
      (params) => {
        params.set("page", page.toString())
        return params
      },
      {
        preventScrollReset: true,
      }
    )
  }

  // TODO: refactor remove effect
  useEffect(() => {
    if (!page) {
      setSearchParams(
        (params) => {
          params.set("page", "1")
          return params
        },
        {
          preventScrollReset: true,
        }
      )
    }
  }, [page, setSearchParams])

  if (isLoading) {
    return <PageLoader />
  }

  if (isError) {
    return <PageError />
  }

  const watchList = data?.myWatchList?.data

  const pagination = data?.myWatchList?.paginatorInfo

  return (
    <main className="px-2 pt-[10vh] lg:px-8">
      <h1 className="mb-8 text-4xl font-bold">My Watchlist</h1>
      <div className="grid grid-cols-12 gap-8">
        {watchList?.map((watchList, index) => {
          if (watchList?.watch_listable?.__typename === "Movie") {
            return (
              <WatchListMovie key={index} data={watchList.watch_listable} />
            )
          } else if (watchList.watch_listable?.__typename === "TVShowSeason") {
            return (
              <WatchListSeason key={index} data={watchList.watch_listable} />
            )
          } else if (
            watchList.watch_listable?.__typename === "TVShowEpisodes"
          ) {
            return (
              <WatchListEpisode key={index} data={watchList.watch_listable} />
            )
          }
        })}
      </div>
      {pagination && pagination?.lastPage > 1 && (
        <Pagination className="mt-8">
          <PaginationContent>
            {Array.from({ length: pagination.lastPage }, (_, i) => i + 1).map(
              (item) => {
                if (
                  Math.abs(item - page) <= 1 ||
                  item === 1 ||
                  item === pagination.lastPage ||
                  (item % 3 === 0 && Math.abs(item - page) <= 2)
                ) {
                  return (
                    <PaginationItem key={item}>
                      <Button
                        onClick={() => handlePageChange(item)}
                        variant="outline"
                        className={cn(
                          {
                            "bg-red-500 text-white hover:bg-red-500/90":
                              item === page,
                          },
                          {
                            "bg-white text-black hover:bg-white/90":
                              item !== page,
                          }
                        )}
                      >
                        {item}
                      </Button>
                    </PaginationItem>
                  )
                } else if (
                  item === 2 ||
                  item === pagination.lastPage - 1 ||
                  (item % 3 === 1 && Math.abs(item - page) < 2)
                ) {
                  return <PaginationEllipsis key={item} />
                }
              }
            )}
          </PaginationContent>
        </Pagination>
      )}
    </main>
  )
}

export default MyWatchList
