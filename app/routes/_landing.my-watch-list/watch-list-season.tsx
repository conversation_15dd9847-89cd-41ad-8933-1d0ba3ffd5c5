import { BlurhashImage } from "@/components/common"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { type FragmentType, graphql, useFragment } from "@/gql"
import { useLanguage } from "@/lib/store"
import { baseUrl } from "@/lib/utils"
import { Link } from "react-router"

const WATCH_LIST_SEASON_FRAGMENT = graphql(`
  fragment WatchListSeasonFragment on TVShowSeason {
    id
    title
    description_mz
    description_en
    tvShow {
      slug
      id
      title
      imageLandscape {
        id
        hash
        path
      }
    }
  }
`)

interface Props {
  data: FragmentType<typeof WATCH_LIST_SEASON_FRAGMENT>
}

const WatchListSeason = ({ data }: Props) => {
  const season = useFragment(WATCH_LIST_SEASON_FRAGMENT, data)
  const { language } = useLanguage()

  if (!season.tvShow?.id) {
    return (
      <div className="col-span-6 hover:bg-accent">
        <div className="grid grid-cols-12">
          <div className="col-span-4">
            <AspectRatio ratio={16 / 9}>
              <img
                alt="not found"
                className="size-full object-cover"
                src="/not_found.jpg"
              />
            </AspectRatio>
          </div>
          <div className="col-span-8 px-4">
            <p>Content no longer available</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <Link
      // key={watchList.watch_listable.id}
      className="col-span-6 hover:bg-accent"
      to={`/show/${season?.tvShow?.slug}?sid=${season.id}`}
    >
      <div className="grid grid-cols-12">
        <div className="col-span-4">
          <AspectRatio ratio={16 / 9}>
            <BlurhashImage
              className="size-full object-cover"
              alt={season.title}
              src={`${baseUrl}/image/small/${season?.tvShow?.imageLandscape?.path}`}
              blurhash={season?.tvShow?.imageLandscape?.hash || ""}
            />
          </AspectRatio>
        </div>
        <div className="col-span-8 px-4">
          <p>
            {season?.tvShow?.title} - {season.title}
          </p>
          <p className="line-clamp-6 whitespace-pre-wrap text-sm">
            {language === "en" ? season.description_en : season.description_mz}
          </p>
        </div>
      </div>
    </Link>
  )
}

export default WatchListSeason
