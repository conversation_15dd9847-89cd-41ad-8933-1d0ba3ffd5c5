import { BlurhashImage } from "@/components/common"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { type FragmentType, graphql, useFragment } from "@/gql"
import { useLanguage } from "@/lib/store"
import { baseUrl } from "@/lib/utils"
import { Link } from "react-router"

const WATCH_LIST_MOVIE_FRAGMENT = graphql(`
  fragment WatchListMovieFragment on Movie {
    id
    slug
    title
    description_en
    description_mz
    imageLandscape {
      id
      hash
      path
    }
  }
`)

interface Props {
  data: FragmentType<typeof WATCH_LIST_MOVIE_FRAGMENT>
}

const WatchListMovie = ({ data }: Props) => {
  const movie = useFragment(WATCH_LIST_MOVIE_FRAGMENT, data)
  const { language } = useLanguage()

  if (!movie.id) {
    return (
      <div className="col-span-6 hover:bg-accent">
        <div className="grid grid-cols-12">
          <div className="col-span-4">
            <AspectRatio ratio={16 / 9}>
              <img
                alt="not found"
                className="size-full object-cover"
                src="/not_found.jpg"
              />
            </AspectRatio>
          </div>
          <div className="col-span-8 px-4">
            <p>Content no longer available</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <Link className="col-span-6 hover:bg-accent" to={`/movie/${movie.slug}`}>
      <div className="grid grid-cols-12">
        <div className="col-span-4">
          <AspectRatio ratio={16 / 9}>
            <BlurhashImage
              className="size-full object-cover"
              alt={movie.title}
              src={`${baseUrl}/image/small/${movie.imageLandscape?.path}`}
              blurhash={movie.imageLandscape?.hash || ""}
            />
          </AspectRatio>
        </div>
        <div className="col-span-8 px-4">
          <p>{movie.title}</p>
          <p className="line-clamp-6 whitespace-pre-wrap text-sm">
            {language === "en" ? movie.description_en : movie.description_mz}
          </p>
        </div>
      </div>
    </Link>
  )
}

export default WatchListMovie
