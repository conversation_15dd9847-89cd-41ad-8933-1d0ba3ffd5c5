import {
  type ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { graphql } from "@/gql"
import { ReportableType } from "@/gql/graphql"
import { calculateProducerShare, graphqlClient } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"
import { useLoaderData, redirect } from "react-router"
import { Bar, BarChart, CartesianGrid, XAxis } from "recharts"

import type { Route } from "../_mobile.stats_.$slug/+types/route"
import { useQuery } from "@tanstack/react-query"
import { formatIndianCurrency } from "@/lib/utils/format-currency"

const CONTENT_REPORT = graphql(`
  query GetContentReport($contentType: ReportableType!, $slug: String!) {
    getContentReport(slug: $slug, content_type: $contentType) {
      producer_share
      stats {
        purchase_count
        title
        total_revenue
      }
    }
  }
`)

export const loader = ({ params, request }: Route.LoaderArgs) => {
  const url = new URL(request.url)
  const searchParams = new URLSearchParams(url.search)

  const contentType = searchParams.get("content_type")
  const slug = params.slug

  if (!contentType) {
    redirect("/")
  } else if (
    contentType !== "MOVIE" &&
    contentType !== "LIVE" &&
    contentType !== "TV_SHOW_SEASON"
  ) {
    redirect("/")
  }

  return {
    contentType,
    slug,
  }
}

const Stats = () => {
  const { contentType, slug } = useLoaderData<typeof loader>()

  const { data, isLoading, isError } = useQuery({
    queryKey: ["basic-stats", contentType],
    queryFn: async () =>
      graphqlClient.request(CONTENT_REPORT, {
        contentType: contentType! as ReportableType,
        slug: slug,
      }),
    enabled: !!contentType || !!slug,
  })

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        Loading...
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex h-screen items-center justify-center">
        Error receiving stats
      </div>
    )
  }

  const contentReport = data?.getContentReport
  const chartConfig = {
    purchase_count: {
      label: "Purchase count",
      color: "#fcd373",
    },
    total_revenue: {
      label: "Total revenue",
      color: "#fab40f",
    },
  } satisfies ChartConfig

  return (
    <main>
      <div className="flex h-full min-h-screen flex-col items-center gap-y-4 pt-[15vh]">
        {contentReport?.stats && (
          <>
            <div className="mx-auto grid w-4/5 grid-cols-12 rounded-lg border border-white md:w-1/3">
              <p className="col-span-5 border-r-2 border-white p-2">
                Total purchase
              </p>
              <p className="col-span-7 p-2">
                {contentReport.stats?.[0].purchase_count}
              </p>

              <div className="col-span-12 border border-white" />

              <p className="col-span-5 border-r-2 border-white p-2">
                Total Revenue
              </p>

              <p className="col-span-7 p-2">
                {contentReport.stats?.[0].total_revenue
                  ? formatIndianCurrency(contentReport.stats?.[0].total_revenue)
                  : 0}
              </p>

              <div className="col-span-12 border border-white" />

              <p className="col-span-5 border-r-2 border-white p-2">
                Producer share ({contentReport.producer_share}%)
              </p>
              <p className="col-span-7 p-2">
                {calculateProducerShare({
                  producerShare: contentReport.producer_share,
                  stats: contentReport.stats,
                })}
              </p>

              <div className="col-span-12 border border-white" />

              <p className="col-span-5 border-r-2 border-white p-2">
                Studio share
              </p>
              <p className="col-span-7 p-2">
                {formatIndianCurrency(
                  Math.abs(
                    Number.parseFloat(
                      calculateProducerShare({
                        producerShare: contentReport.producer_share,
                        stats: contentReport.stats,
                        returnType: "number",
                      })
                    ) - contentReport.stats?.[0]?.total_revenue
                  )
                )}
              </p>
            </div>
            <ChartContainer
              config={chartConfig}
              className={cn("h-[300px] w-full", {
                "w-full lg:w-1/5": contentReport.stats.length < 2,
                "w-3/4": contentReport.stats.length >= 2,
              })}
            >
              <BarChart accessibilityLayer data={contentReport.stats}>
                <CartesianGrid />
                <XAxis
                  dataKey="title"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  className="text-lg"
                  // tickFormatter={(value) => value.slice(0, 3)}
                />
                <ChartTooltip content={<ChartTooltipContent />} />
                <ChartLegend content={<ChartLegendContent />} />
                <Bar
                  fill="var(--color-purchase_count)"
                  radius={4}
                  dataKey="purchase_count"
                />
                <Bar
                  fill="var(--color-total_revenue)"
                  radius={4}
                  dataKey="total_revenue"
                />
              </BarChart>
            </ChartContainer>
          </>
        )}
      </div>
    </main>
  )
}

export default Stats
