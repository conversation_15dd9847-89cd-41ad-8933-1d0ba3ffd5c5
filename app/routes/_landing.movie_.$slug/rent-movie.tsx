import { Rupee } from "@/components/common"
import { But<PERSON>, buttonVariants } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTrigger,
} from "@/components/ui/dialog"
import { RENT_MOVIE } from "@/lib/graphql/mutation"
import { useRazorpay, useUser } from "@/lib/hooks"
import { graphqlClient } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"
import { useNavigate } from "react-router"
import { useState } from "react"
import { useMutation } from "@tanstack/react-query"

interface Props {
  price: number
  movieId: string
}

const RentMovie = ({ price, movieId }: Props) => {
  const [open, setOpen] = useState(false)
  const navigate = useNavigate()
  const { handleRazorpay } = useRazorpay()

  const { data: user } = useUser()

  const rentMovie = useMutation({
    mutationFn: async () =>
      await graphqlClient.request(RENT_MOVIE, {
        id: movieId,
        redirectURL: window.location.href,
      }),
    onSuccess: (data) => {
      if (data?.rentMovie?.__typename === "PhonePePaymentResponse") {
        navigate(data.rentMovie.goto_url)
      }
      if (data?.rentMovie?.__typename === "RazorPayPaymentResponse") {
        handleRazorpay({
          price: price,
          orderId: data?.rentMovie.order_id,
          user: user?.getMe,
          onLoadCallback: () => handleOpenChange(false),
          redirectUrl: window.location.href,
        })
      }
    },
  })

  const handleRentMovie = () => {
    rentMovie.mutate()
  }

  const handleOpenChange = (state: boolean) => {
    setOpen(state)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger
        className={cn(buttonVariants({ variant: "default" }), "w-full")}
      >
        Rent <Rupee amount={price} />
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>Disclaimer:</DialogHeader>
        <DialogDescription>
          <span className="block">
            {`The Copyright Act, 1957 (the "Act") came into effect from
                January 1958. The Act has been amended five times since then,
                i.e. in 1983, 1984, 1992, 1994, 1999 and 2012. The Copyright
                (Amendment) Act, 2012 is the most substantial.`}
          </span>
          <span className="block">
            {`Rented movie will be available for 72 hours from the time of
                purchase. Refund is not available for any item purchased. In
                case of failed payments or other issues, please contact our
                customer support.`}
          </span>
        </DialogDescription>
        <Button isLoading={rentMovie.isPending} onClick={handleRentMovie}>
          Rent <Rupee amount={price} />
        </Button>
      </DialogContent>
    </Dialog>
  )
}

export default RentMovie
