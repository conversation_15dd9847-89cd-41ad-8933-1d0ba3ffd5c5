import { baseUrl, formatDuration, graphqlClient } from "@/lib/utils"
import MovieAction from "./movie-action"
import { FavoriteIcon } from "@/components/icons"
import { But<PERSON> } from "@/components/ui/button"
import { useWatchList } from "@/lib/hooks"
import { WatchListableType } from "@/gql/graphql"
import { toast } from "@/components/ui/use-toast"
import { useLanguage } from "@/lib/store"
import {
  BlurhashImage,
  CastModal,
  MuxPlayerTrailer,
  PageError,
  PageLoader,
} from "@/components/common"
import { useParams } from "react-router"
import { graphql } from "@/gql"
import type { Route } from "./+types/route"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import MOVIE_BY_SLUG_QUERY from "@/lib/graphql/queries/movie-by-slug-query"

const MOVIE_BY_SLUG_SERVER = graphql(`
  query MovieBySlugServer($slug: String!) {
    getMovieBySlug(slug: $slug) {
      id
      title
      description_en
      imageLandscape {
        path
      }
    }
  }
`)

export async function loader({ params }: Route.LoaderArgs) {
  const response = await graphqlClient.request(MOVIE_BY_SLUG_SERVER, {
    slug: params.slug,
  })

  const movie = response?.getMovieBySlug

  if (movie) {
    return { movie }
  } else {
    throw new Error("404. Not Found")
  }
}

export const meta = ({ data, location }: Route.MetaArgs) => {
  return [
    { title: `Enila - ${data?.movie?.title}` },
    { name: "description", content: `${data?.movie?.description_en}` },
    {
      property: "og:title",
      content: `Enila - ${data?.movie?.title}`,
    },
    {
      property: "og:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      property: "og:image",
      itemProp: "image",
      content: data?.movie?.imageLandscape?.path
        ? `${baseUrl}/image/small/${data?.movie?.imageLandscape?.path}`
        : "",
    },
    {
      property: "og:image:height",
      content: "600",
    },
    {
      property: "og:image:width",
      content: "600",
    },
    {
      property: "robots",
      content: "index,follow",
    },
    {
      property: "og:description",
      content: `${data?.movie?.description_en}`,
    },
    {
      property: "twitter:card",
      content: "summary_large_image",
    },
    {
      property: "twitter:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "twitter:title",
      content: `Enila - ${data?.movie?.title}`,
    },
    {
      property: "twitter:description",
      content: `${data?.movie?.description_en}`,
    },
    {
      property: "twitter:image",
      content: data?.movie?.imageLandscape?.path
        ? `${baseUrl}/image/small/${data?.movie?.imageLandscape?.path}`
        : "",
    },
    {
      property: "og:site_name",
      content: "Enila",
    },
  ]
}

const MovieBySlug = () => {
  const { slug } = useParams()
  const queryClient = useQueryClient()

  const { language } = useLanguage()

  const { data, isLoading, isError } = useQuery({
    queryKey: ["movie-by-slug", slug],
    queryFn: async () => graphqlClient.request(MOVIE_BY_SLUG_QUERY, { slug: slug! }),
    enabled: !!slug,
  })

  const { addToWatchList, deleteFromWatchList } = useWatchList()

  const handleAddToWatchList = () => {
    const id = data?.getMovieBySlug?.id
    const movieTitle = data?.getMovieBySlug?.title
    if (id) {
      addToWatchList.mutate(
        {
          id: id,
          contentType: WatchListableType.Movie,
        },
        {
          onSuccess: () => {
            void queryClient.refetchQueries({
              queryKey: ["movie-by-slug"],
            })
            toast({
              description: `${movieTitle} added to watch list`,
              variant: "default",
            })
          },
        }
      )
    }
  }

  const handleDeleteFromWatchList = () => {
    const id = data?.getMovieBySlug?.isAddedToWatchList
    const movieTitle = data?.getMovieBySlug?.title
    if (id) {
      deleteFromWatchList.mutate(
        {
          id: id.toString(),
        },
        {
          onSuccess: () => {
            void queryClient.refetchQueries({
              queryKey: ["movie-by-slug"],
            })
            toast({
              description: `${movieTitle} removed from watchlist`,
              variant: "default",
            })
          },
        }
      )
    }
  }

  if (isLoading) {
    return <PageLoader />
  }

  if (isError) {
    return <PageError />
  }

  return (
    <div className="relative h-screen overflow-hidden">
      {data?.getMovieBySlug?.trailer?.cdn_playback_id ? (
        <>
          <MuxPlayerTrailer
            playbackId={data.getMovieBySlug.trailer.cdn_playback_id}
            posterUrl={data?.getMovieBySlug.imageLandscape?.path || ""}
          />
        </>
      ) : (
        <>
          <BlurhashImage
            className="size-full object-cover"
            src={`${baseUrl}/image/medium/${data?.getMovieBySlug?.imageLandscape?.path}`}
            alt={data?.getMovieBySlug?.title || ""}
            blurhash={data?.getMovieBySlug?.imageLandscape?.hash || ""}
          />
        </>
      )}
      <div className="pointer-events-none absolute inset-0 bg-gradient-to-r from-black/25 to-black/0" />
      <div className="pointer-events-none absolute inset-0 bg-gradient-to-b from-black/25 to-black/0" />
      <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-black/25" />

      <div className="absolute top-[40%] z-40 w-1/2 space-y-2 px-8 xl:w-2/5 2xl:w-1/3">
        <h1 className="text-4xl font-bold">{data?.getMovieBySlug?.title}</h1>
        <p className="description-scrollbar h-auto max-h-32 overflow-y-auto whitespace-pre-line pr-2">
          {language === "en"
            ? data?.getMovieBySlug?.description_en
            : data?.getMovieBySlug?.description_mz}
        </p>

        <p className="description-scrollbar line-clamp-2 h-auto max-h-16 overflow-y-clip pr-2">
          {data?.getMovieBySlug?.casts}
        </p>
        <div className="flex w-full justify-end pr-2">
          <CastModal cast={data?.getMovieBySlug?.casts || ""} />
        </div>
        {data?.getMovieBySlug?.genre ? (
          <div>
            <span className="font-bold">Genre: </span>
            {data.getMovieBySlug.genre}
          </div>
        ) : null}
        <div className="flex font-semibold">
          <span className="border-r-2 border-white pr-2">
            {data?.getMovieBySlug?.production_year}
          </span>
          {data?.getMovieBySlug?.duration ? (
            <span className="border-r-2 border-white px-2">
              {formatDuration(data.getMovieBySlug.duration)}
            </span>
          ) : null}
          <span className="px-2">{data?.getMovieBySlug?.age_label}</span>
        </div>

        {data?.getMovieBySlug && <MovieAction data={data.getMovieBySlug} />}

        {data?.getMovieBySlug?.isAddedToWatchList ? (
          <Button className="space-x-2" onClick={handleDeleteFromWatchList} variant="ghost">
            <span>Remove from watchlist</span>
            <FavoriteIcon fill={true} color="theme-yellow" />
          </Button>
        ) : (
          <Button className="space-x-2" onClick={handleAddToWatchList} variant="ghost">
            <span>Add to watchlist </span>
            <FavoriteIcon />
          </Button>
        )}
      </div>
    </div>
  )
}

export default MovieBySlug
