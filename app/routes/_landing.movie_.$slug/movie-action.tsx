import { Button, buttonVariants } from "@/components/ui/button"
import { type FragmentType, graphql, useFragment } from "@/gql"
import { Link, useNavigate } from "react-router"
import RentMovie from "./rent-movie"
import { cn } from "@/lib/utils/cn"

const MovieActionFragment = graphql(`
  fragment MovieAction on Movie {
    id
    for_subscriber
    is_rented
    is_free
    price
    continueWatching {
      watched_duration
    }
  }
`)

interface Props {
  data: FragmentType<typeof MovieActionFragment>
}

const MovieAction = ({ data }: Props) => {
  const navigate = useNavigate()
  const movie = useFragment(MovieActionFragment, data)

  const handlePlay = () => {
    void navigate("watch")
  }

  if (movie && movie.for_subscriber) {
    if (movie.is_rented) {
      return (
        <div className="flex">
          <Button onClick={handlePlay} className="">
            {movie.continueWatching?.watched_duration ? "Resume" : "Play"}
          </Button>
        </div>
      )
    } else {
      return (
        <div className="flex">
          <Link
            to={`/subscription?ref=${window.location.pathname}${window.location.search}`}
            className={cn(buttonVariants({ variant: "default" }), "w-full")}
          >
            Subscribe to Play
          </Link>
        </div>
      )
    }
  } else if (movie?.is_free || movie?.is_rented) {
    return (
      <div className="flex">
        <Button onClick={handlePlay} className="w-full">
          {movie.continueWatching?.watched_duration ? "Resume" : "Play"}
        </Button>
      </div>
    )
  } else if (movie?.price) {
    return (
      <div className="flex w-full">
        <RentMovie price={movie.price} movieId={movie.id} />
      </div>
    )
  }
  return null
}

export default MovieAction
