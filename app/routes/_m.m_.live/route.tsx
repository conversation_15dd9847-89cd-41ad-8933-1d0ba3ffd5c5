import { BlurhashImage, MobileError, MobileLoading } from "@/components/common"
import { SearchIcon } from "@/components/icons"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { graphql } from "@/gql"
import { baseUrl, graphqlClient } from "@/lib/utils"
import { DEBOUNCE_TIMER, MOBILE_FETCH_NUMBER } from "@/lib/utils/constants"
import { useState } from "react"
import { useDebounce } from "use-debounce"
import LiveDetailDialog from "./live-detail-dialog"
import type { Live } from "@/gql/graphql"
import { useInfiniteQuery } from "@tanstack/react-query"

const LIVE_SHOWS = graphql(`
  query LiveShows($first: Int, $page: Int, $keyword: String) {
    getLives(first: $first, page: $page, keyword: $keyword) {
      data {
        id
        title
        imagePortrait {
          id
          path
          hash
        }
        imageLandscape {
          id
          path
          hash
        }
      }
      paginatorInfo {
        hasMorePages
        currentPage
      }
    }
  }
`)

const LiveShows = () => {
  const [search, setSearch] = useState("")

  const [debouncedSearch] = useDebounce(search, DEBOUNCE_TIMER)
  const [open, setOpen] = useState(false)
  const [selectedLive, setSelectedLive] = useState<Partial<Live>>({})

  const { data, isFetching, isError, hasNextPage, fetchNextPage } = useInfiniteQuery({
    queryKey: ["live-shows", debouncedSearch],
    queryFn: async ({ pageParam = 1 }: { pageParam: number }) =>
      graphqlClient.request(LIVE_SHOWS, {
        first: MOBILE_FETCH_NUMBER,
        page: pageParam,
        keyword: debouncedSearch,
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.getLives?.paginatorInfo?.hasMorePages
        ? lastPage.getLives?.paginatorInfo?.currentPage + 1
        : undefined
    },
  })

  const handleOpenChange = (open: boolean) => {
    setOpen(open)
  }

  const handleSelectedLive = (live: Partial<Live>) => {
    setSelectedLive(live)
    setOpen(true)
  }

  return (
    <>
      <div className="grid grid-cols-2 gap-4">
        <div className="col-span-2 w-full">
          <Input
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            placeholder="Search"
            leftSection={<SearchIcon />}
          />
        </div>
        {isError && <MobileError className="col-span-2" />}
        {!isError &&
          data?.pages?.flatMap((page) =>
            page?.getLives?.data?.map((live) => {
              return (
                <div key={live.id} className="col-span-1">
                  <AspectRatio ratio={3 / 4}>
                    <Button
                      onClick={() => handleSelectedLive(live)}
                      key={live.id}
                      className="flex size-full flex-col p-0"
                      variant="ghost"
                    >
                      <BlurhashImage
                        className="size-full object-cover"
                        alt={live?.title}
                        src={`${baseUrl}/image/medium/${live?.imagePortrait?.path}`}
                        blurhash={live?.imagePortrait?.hash || ""}
                      />
                    </Button>
                  </AspectRatio>
                  <h2 className="px-1 text-center text-sm">{live?.title}</h2>
                </div>
              )
            })
          )}
        {isFetching && <MobileLoading className="col-span-1" />}
        {hasNextPage && (
          <div className="col-span-2 mx-auto">
            <Button isLoading={isFetching} onClick={() => void fetchNextPage()} className="col-span-2">
              Load more
            </Button>
          </div>
        )}
      </div>
      <LiveDetailDialog
        open={open}
        handleOpenChange={handleOpenChange}
        selectedLive={selectedLive}
      />
    </>
  )
}

export default LiveShows
