import { BlurhashImage } from "@/components/common"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
} from "@/components/ui/dialog"
import type { Live } from "@/gql/graphql"
import { RENT_LIVE } from "@/lib/graphql/mutation"
import { useRazorpay, useUser } from "@/lib/hooks"
import { baseUrl, graphqlClient } from "@/lib/utils"
import { useNavigate } from "react-router"
import { ClientError } from "graphql-request"
import { useMutation } from "@tanstack/react-query"

interface Props {
  open: boolean
  handleOpenChange: (open: boolean) => void
  selectedLive: Partial<Live>
}

const LiveDetailDialog = ({ selectedLive, open, handleOpenChange }: Props) => {
  const navigate = useNavigate()
  const { handleRazorpay } = useRazorpay()

  const { data: user } = useUser()

  const rentLive = useMutation({
    mutationFn: async () =>
      await graphqlClient.request(RENT_LIVE, {
        id: selectedLive.id!,
      }),
    onSuccess: (data) => {
      if (data?.rentLive?.__typename === "PhonePePaymentResponse") {
        navigate(data.rentLive.goto_url)
      }
      if (data?.rentLive?.__typename === "RazorPayPaymentResponse") {
        handleRazorpay({
          price: selectedLive?.price || 0,
          orderId: data?.rentLive.order_id,
          user: user?.getMe,
          onLoadCallback: () => handleOpenChange(false),
        })
      }
    },
    onError: (err) => {
      if (err instanceof ClientError) {
        console.log(err)
      }
    },
  })

  const handleRentLive = () => {
    rentLive.mutate()
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent>
        <AspectRatio ratio={16 / 9}>
          <BlurhashImage
            className="size-full object-cover"
            alt={selectedLive?.title || ""}
            src={`${baseUrl}/image/medium/${selectedLive?.imageLandscape?.path}`}
            blurhash={selectedLive?.imageLandscape?.hash || ""}
          />
          {/* <img */}
          {/*   className="size-full object-cover" */}
          {/*   alt={selectedLive?.title} */}
          {/*   src={`${baseUrl}/image/medium/${selectedLive?.imageLandscape?.path}`} */}
          {/* /> */}
        </AspectRatio>
        <DialogHeader className="font-bold">{selectedLive?.title}</DialogHeader>
        <DialogDescription className="text-sm">
          Purchase content can be watched with EN ILA mobile app available on
          the App store or Play store or a desktop browser.
          <br />
          Purchase will be available for 72 hours from the time of purchase.
          Refund is not available for any item purchased. In case of failed
          payments or other issues, please contact our customer support.
          <br />
          For more information refer to our{" "}
          <a href="/refund-policy" className="text-theme-blue">
            refund policy
          </a>
        </DialogDescription>
        <Button isLoading={rentLive.isPending} onClick={handleRentLive}>
          Rent
        </Button>
      </DialogContent>
    </Dialog>
  )
}

export default LiveDetailDialog
