export const meta = () => {
  return [{ title: "Enila - Refund Policy" }]
}

const RefundPolicy = () => {
  return (
    <div className="mx-auto flex h-full min-h-screen max-w-4xl flex-col px-4 pt-[10vh] lg:px-0">
      <h1 className="text-4xl font-bold">Refund Policy</h1>
      <p className="mt-4">
        At Enila, we strive to provide the best streaming experience for our
        users. However, we understand that technical issues may occasionally
        prevent you from enjoying your purchased content. This refund policy
        outlines the conditions under which refunds may be issued for unwatched
        purchases due to technical problems.
      </p>

      <h2 className="mt-4 font-bold">1. Eligibility for Refunds:</h2>
      <p>Refunds are only applicable under the following conditions:</p>
      <ul className="ml-4 list-outside list-disc">
        <li>
          <span className="font-bold">Unwatched Purchase: </span>
          The purchased content has not been watched or partially watched.
        </li>
        <li>
          <span className="font-bold">Technical Problems: </span>
          {`The inability to watch the purchased content is due to technical
          problems originating from Enila's platform, including but not limited
          to:`}
          <ul className="ml-4 list-outside list-disc">
            <li>Streaming issues</li>
            <li>Server errors</li>
            <li>Account access problems</li>
          </ul>
        </li>
      </ul>

      <h2 className="mt-4 font-bold">2. Non-Refundable Circumstances:</h2>
      <p>Refunds will not be issued in the following scenarios:</p>
      <ul className="ml-4 list-outside list-disc">
        <li>The content has been partially or fully watched.</li>
        <li>
          {`Technical issues caused by the user's hardware, software, or internet
          connection.`}
        </li>
        <li>
          Requests made beyond the stipulated time frame{" "}
          <a href="#section-4">(see section 4).</a>
        </li>
        <li>Dislike of content or change of mind.</li>
      </ul>

      <h2 className="mt-4 font-bold">3. How to Request a Refund:</h2>
      <p>To request a refund, please follow these steps:</p>
      <ol className="ml-4 list-outside list-decimal">
        <li>
          <span className="font-bold">Contact Customer Support: </span>
          Reach out to our customer support team via email or Phone call.
        </li>
        <li>
          <span className="font-bold">Provide Necessary Information: </span>
          Include the following details in your refund request:
          <ul className="ml-4 list-outside list-disc">
            <li>Your account details</li>
            <li>Title of the purchased content</li>
            <li>Date and time of purchase</li>
            <li>Detailed description of the technical problem encountered</li>
          </ul>
        </li>
        <li>
          <span className="font-bold">Verification Process: </span>
          Our support team will verify the technical issue and confirm that the
          content has not been watched.
        </li>
      </ol>

      <h2 id="section-4" className="mt-4 font-bold">
        4. Time Frame for Refund Requests:
      </h2>
      <p>
        Refund requests must be submitted within 14 days of the purchase date.
        Requests made after this period will not be eligible for a refund.
      </p>

      <h2 className="mt-4 font-bold">5. Processing Refunds:</h2>
      <p>
        Once your refund request is approved, the refund will be processed
        within 5-7 business days. The refunded amount will be credited back to
        the original payment method used at the time of purchase.
      </p>

      <h2 className="mt-4 font-bold">6. Changes to the Refund Policy:</h2>
      <p className="pb-8">
        Enila reserves the right to amend this refund policy at any time. Any
        changes will be posted on our website and will be effective immediately
        upon posting.
      </p>
    </div>
  )
}

export default RefundPolicy
