import { Rupee } from "@/components/common"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
} from "@/components/ui/dialog"
import { RENT_LIVE } from "@/lib/graphql/mutation"
import { useRazorpay, useUser } from "@/lib/hooks"
import { useLiveRentDialogStore } from "@/lib/store"
import { graphqlClient } from "@/lib/utils"
import { useNavigate } from "react-router"
import { ClientError } from "graphql-request"
import { useMutation } from "@tanstack/react-query"

const RentLiveDialog = () => {
  const navigate = useNavigate()
  const { open, openChange, price, id, closeLive } = useLiveRentDialogStore()
  const { handleRazorpay } = useRazorpay()

  const { data: user } = useUser()

  const rentLive = useMutation({
    mutationFn: async () =>
      await graphqlClient.request(RENT_LIVE, {
        id: id,
      }),
    onSuccess: (data) => {
      if (data?.rentLive?.__typename === "PhonePePaymentResponse") {
        void navigate(data.rentLive.goto_url)
      }
      if (data?.rentLive?.__typename === "RazorPayPaymentResponse") {
        handleRazorpay({
          price: price,
          orderId: data?.rentLive.order_id,
          user: user?.getMe,
          onLoadCallback: () => closeLive(),
        })
      }
    },
    onError: (err) => {
      if (err instanceof ClientError) {
        console.log(err)
      }
    },
  })

  const handleRentLive = () => {
    rentLive.mutate()
  }

  return (
    <Dialog open={open} onOpenChange={openChange}>
      <DialogContent>
        <DialogHeader>Disclaimer:</DialogHeader>
        <DialogDescription>
          <span className="block">
            {`The Copyright Act, 1957 (the "Act") came into effect from
                January 1958. The Act has been amended five times since then,
                i.e. in 1983, 1984, 1992, 1994, 1999 and 2012. The Copyright
                (Amendment) Act, 2012 is the most substantial.`}
          </span>
          <span className="block">
            {`Rented movie will be available for 72 hours from the time of
                purchase. Refund is not available for any item purchased. In
                case of failed payments or other issues, please contact our
                customer support.`}
          </span>
        </DialogDescription>
        <Button isLoading={rentLive.isPending} onClick={handleRentLive}>
          Rent <Rupee amount={price} />
        </Button>
      </DialogContent>
    </Dialog>
  )
}

export default RentLiveDialog
