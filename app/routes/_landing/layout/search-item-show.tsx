import { BlurhashImage } from "@/components/common"
import { CommandItem } from "@/components/ui/command"
import { type FragmentType, graphql, useFragment } from "@/gql"
import { baseUrl } from "@/lib/utils"
import { useNavigate } from "react-router"

const SEARCH_ITEM_SHOW_FRAGMENT = graphql(`
  fragment SearchItemShowFragment on TVShow {
    id
    title
    slug
    imageLandscape {
      path
    }
  }
`)

interface Props {
  data: FragmentType<typeof SEARCH_ITEM_SHOW_FRAGMENT>
  closeSearch: () => void
  index: number
}

const SearchItemShow = ({ data, closeSearch, index }: Props) => {
  const tvShow = useFragment(SEARCH_ITEM_SHOW_FRAGMENT, data)
  const navigate = useNavigate()
  return (
    <CommandItem
      value={`${tvShow.title} - ${index}`}
      onSelect={() => {
        navigate(`/show/${tvShow.slug}`)
        closeSearch()
      }}
    >
      <div className="relative flex h-24 w-full cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 hover:bg-red-500 focus:bg-red-500">
        <div className="flex size-full gap-x-4">
          <div className="relative size-full basis-1/3">
            {/* <img */}
            {/*   className="size-full object-cover" */}
            {/*   alt={tvShow.title} */}
            {/*   src={`${baseUrl}/image/thumbnail/${tvShow.imageLandscape?.path}`} */}
            {/* /> */}
            <BlurhashImage
              alt={tvShow?.title}
              src={`${baseUrl}/image/thumbnail/${tvShow.imageLandscape?.path}`}
              blurhash={tvShow.imageLandscape?.hash ?? ""}
            />
          </div>
          <span className="basis-2/3">{tvShow?.title}</span>
        </div>
      </div>
    </CommandItem>
  )
}

export default SearchItemShow
