import { CloseIcon } from "@/components/icons"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandList,
} from "@/components/ui/command"
import { graphql } from "@/gql"
import { graphqlClient } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"
import { Loader2, SearchIcon } from "lucide-react"
import { useEffect, useRef, useState } from "react"
import { useDebounce } from "use-debounce"
import SearchItemMovie from "./search-item-movie"
import SearchItemShow from "./search-item-show"
import SearchItemLive from "./search-item-live"
import { useQuery } from "@tanstack/react-query"
import { ScrollArea } from "@/components/ui/scroll-area"

const SEARCH = graphql(`
  query SearchContent($keyword: String!) {
    searchContent(keyword: $keyword) {
      id
      canSearch {
        __typename
        ...SearchItemMovieFragment
        ...SearchItemShowFragment
        ...SearchItemLiveFragment
        ... on Movie {
          id
          title
          imageLandscape {
            path
          }
        }
        ... on TVShow {
          id
          title
          imageLandscape {
            path
          }
        }
        ... on Live {
          id
          title
          cdn_playback_id
          is_rented
          is_free
          price
          imageLandscape {
            path
          }
        }
      }
    }
  }
`)

const Search = () => {
  const [searchKeyword, setSearchKeyword] = useState("")
  const [openSearch, setOpenSearch] = useState(false)
  const inputRef = useRef<HTMLInputElement | null>(null)
  const [keyword] = useDebounce(searchKeyword, 350)

  const { data, isLoading } = useQuery({
    queryKey: ["search", keyword],
    queryFn: async () =>
      await graphqlClient.request(SEARCH, {
        keyword: keyword,
      }),
    enabled: !!keyword,
  })

  const openSearchMenu = () => {
    setOpenSearch(true)
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }

  const closeSearch = () => {
    setSearchKeyword("")
    setOpenSearch(false)
  }

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && openSearch) {
        e.preventDefault()
        setOpenSearch(false)
      }
    }

    document.addEventListener("keydown", handleKeyDown)

    return () => {
      document.removeEventListener("keydown", handleKeyDown)
    }
  }, [openSearch])

  const searchItems = data?.searchContent ?? []

  return (
    <>
      <div className="relative">
        <Command shouldFilter={false}>
          <CommandInput
            ref={inputRef}
            value={searchKeyword}
            onValueChange={(e) => setSearchKeyword(e)}
            className={cn(
              "flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
              "flex max-w-96 origin-right transform bg-black text-white transition-all duration-150 ease-out",
              {
                "w-0 border-0 p-0": !openSearch,
                "w-96": openSearch,
              }
            )}
          />
          {isLoading && (
            <Loader2 className="absolute right-2 top-2 size-6 animate-spin text-gray-400" />
          )}

          {searchItems && searchItems?.length > 0 && (
            <CommandList>
              <CommandGroup
                className={cn("absolute top-12 bg-black p-0 text-white", {
                  "w-0": !openSearch,
                  "w-96": openSearch,
                })}
              >
                <ScrollArea
                  className="h-full max-h-[400px] overflow-scroll scrollbar-thin"
                  onWheel={(e) => e.stopPropagation()}
                >
                {searchItems.map((item, index) => {
                  if (!item?.canSearch) return null

                  const searchable = item.canSearch

                  switch (searchable.__typename) {
                    case "Movie":
                      return (
                        <SearchItemMovie
                          key={`${item.id}-${index}-${keyword}`}
                          data={searchable}
                          closeSearch={closeSearch}
                          index={index}
                        />
                      )
                    case "TVShow":
                      return (
                        <SearchItemShow
                          key={`${item.id}-${index}-${keyword}`}
                          data={searchable}
                          closeSearch={closeSearch}
                          index={index}
                        />
                      )
                    case "Live":
                      return (
                        <SearchItemLive
                          key={`${item.id}-${index}-${keyword}`}
                          data={searchable}
                          closeSearch={closeSearch}
                          index={index}
                        />
                      )
                    default:
                      return null
                  }
                })}
                </ScrollArea>
              </CommandGroup>
            </CommandList>
          )}
        </Command>
      </div>
      {openSearch ? (
        <Button variant="ghost" onClick={closeSearch}>
          <CloseIcon />
        </Button>
      ) : (
        <Button variant="ghost" onClick={openSearchMenu}>
          <SearchIcon />
        </Button>
      )}
    </>
  )
}

export default Search
