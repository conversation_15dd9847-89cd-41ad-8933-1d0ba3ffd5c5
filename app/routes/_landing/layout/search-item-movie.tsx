import { BlurhashImage } from "@/components/common"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { CommandItem } from "@/components/ui/command"
import { type FragmentType, graphql, useFragment } from "@/gql"
import { baseUrl } from "@/lib/utils"
import { useNavigate } from "react-router"

const SEARCH_ITEM_MOVIE_FRAGMENT = graphql(`
  fragment SearchItemMovieFragment on Movie {
    id
    title
    slug
    imageLandscape {
      path
      hash
    }
  }
`)

interface Props {
  data: FragmentType<typeof SEARCH_ITEM_MOVIE_FRAGMENT>
  closeSearch: () => void
  index: number
}

const SearchItemMovie = ({ data, closeSearch, index }: Props) => {
  const movie = useFragment(SEARCH_ITEM_MOVIE_FRAGMENT, data)
  const navigate = useNavigate()

  return (
    <CommandItem
      value={`${movie.title} - ${index}`}
      onSelect={() => {
        navigate(`/movie/${movie.slug}`)
        closeSearch()
      }}
    >
      <div className="relative flex h-24 w-full cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 hover:bg-red-500 focus:bg-red-500">
        <div className="flex size-full gap-x-4">
          <div className="relative size-full basis-1/3">
            <BlurhashImage
              alt={movie?.title}
              src={`${baseUrl}/image/thumbnail/${movie.imageLandscape?.path}`}
              blurhash={movie.imageLandscape?.hash ?? ""}
            />
          </div>
          <span className="basis-2/3">{movie?.title}</span>
        </div>
      </div>
    </CommandItem>
  )
}

export default SearchItemMovie
