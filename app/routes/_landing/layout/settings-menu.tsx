import { LanguageIcon, LogoutIcon, SettingsIcon, UserIcon } from "@/components/icons"
import { Button, buttonVariants } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useToast } from "@/components/ui/use-toast"
import { graphql } from "@/gql"
import { useUser } from "@/lib/hooks"
import { useLanguage } from "@/lib/store"
import { graphqlClient } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"
import { Link, NavLink, useFetcher } from "react-router"
import { ClientError } from "graphql-request"
import { useMutation, useQueryClient } from "@tanstack/react-query"

const USER_LOGOUT = graphql(`
  mutation UserLogout {
    userLogout {
      message
    }
  }
`)

const CHANGE_LANGUAGE = graphql(`
  mutation UpdateMyInfo($language: String) {
    updateMyInfo(language: $language) {
      id
      language
    }
  }
`)

const SettingsMenu = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()
  const fetcher = useFetcher()

  const { data: user } = useUser()

  const { language, setLanguage } = useLanguage()

  const changeLanguage = useMutation({
    mutationFn: async () =>
      await graphqlClient.request(CHANGE_LANGUAGE, {
        language: user?.getMe?.language === "mz" ? "en" : "mz",
      }),
    onSuccess: async (data) => {
      await queryClient.refetchQueries({
        queryKey: ["get-user"],
      })
      const language = data?.updateMyInfo?.language === "en" ? "English" : "Mizo"
      toast({
        description: `Language changed to ${language}`,
        variant: "default",
      })
    },
    onError: (err) => {
      if (err instanceof ClientError) {
        toast({
          description: err.message,
        })
      }
    },
  })

  const userLogout = useMutation({
    mutationFn: async () => {
      await graphqlClient.request(USER_LOGOUT)
    },
  })

  const handleLogout = () => {
    userLogout.mutate(undefined, {
      onError: () => {
        void fetcher.submit(null, {
          action: "/logout",
          method: "POST",
        })
      },
      onSuccess: () => {
        void queryClient.invalidateQueries()
        void fetcher.submit(null, {
          action: "/logout",
          method: "POST",
        })
      },
    })
  }

  const handleChangeLanguage = () => {
    if (user?.getMe?.id) {
      changeLanguage.mutate()
    } else {
      if (language === "en") {
        setLanguage("mz")
      } else {
        setLanguage("en")
      }
    }
  }

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost">
          <SettingsIcon />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuLabel>Settings</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <Button
            variant="ghost"
            className={cn(buttonVariants({ variant: "ghost" }), "flex w-full justify-start")}
            onClick={handleChangeLanguage}
          >
            <LanguageIcon className="mr-2 size-4" /> Language | {language}
          </Button>
        </DropdownMenuItem>
        {user?.getMe && (
          <DropdownMenuItem asChild>
            <Link
              to="/subscription"
              className={cn(buttonVariants({ variant: "ghost" }), "flex w-full justify-start")}
            >
              <LanguageIcon className="mr-2 size-4" /> Subscription
            </Link>
          </DropdownMenuItem>
        )}
        {user?.getMe && (
          <DropdownMenuItem asChild>
            <Link
              to="/my-watch-list"
              className={cn(buttonVariants({ variant: "ghost" }), "flex w-full justify-start")}
            >
              <UserIcon className="mr-2 size-4" /> My Watch List
            </Link>
          </DropdownMenuItem>
        )}
        {user?.getMe && (
          <DropdownMenuItem asChild>
            <Link
              to="/user"
              className={cn(buttonVariants({ variant: "ghost" }), "flex w-full justify-start")}
            >
              <UserIcon className="mr-2 size-4" /> Profile
            </Link>
          </DropdownMenuItem>
        )}
        <DropdownMenuItem asChild>
          {user?.getMe ? (
            <Button
              variant="ghost"
              type="button"
              onClick={handleLogout}
              className={cn(buttonVariants({ variant: "ghost" }), "flex w-full justify-start")}
              isLoading={userLogout.isPending}
            >
              <LogoutIcon className="mr-2 size-4" /> Logout
            </Button>
          ) : (
            <NavLink
              className={cn(buttonVariants({ variant: "ghost" }), "flex w-full justify-start")}
              to="/login"
            >
              <LogoutIcon className="mr-2 size-4" /> Login
            </NavLink>
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default SettingsMenu
