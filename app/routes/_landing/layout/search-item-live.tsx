import { CommandItem } from "@/components/ui/command"
import { type FragmentType, graphql, useFragment } from "@/gql"
import { useLiveRentDialogStore } from "@/lib/store"
import { baseUrl } from "@/lib/utils"
import { useNavigate } from "react-router"

const SEARCH_ITEM_LIVE_FRAGMENT = graphql(`
  fragment SearchItemLiveFragment on Live {
    id
    title
    slug
    cdn_playback_id
    is_rented
    is_free
    price
    for_subscriber
    imageLandscape {
      path
    }
  }
`)

interface Props {
  data: FragmentType<typeof SEARCH_ITEM_LIVE_FRAGMENT>
  closeSearch: () => void
  index: number
}

const SearchItemLive = ({ data, closeSearch, index }: Props) => {
  const live = useFragment(SEARCH_ITEM_LIVE_FRAGMENT, data)
  const navigate = useNavigate()
  const { openLive } = useLiveRentDialogStore()

  const handleSelect = () => {
    if (live.is_free || live.is_rented) {
      navigate(`/live/${live?.slug}`)
      closeSearch()
    } else if (live.for_subscriber) {
      navigate("/subscription")
      closeSearch()
    } else {
      closeSearch()
      openLive(live.id, live.price)
    }
  }
  return (
    <CommandItem value={`${live.title} - ${index}`} onSelect={handleSelect}>
      <div className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 hover:bg-red-500 focus:bg-red-500">
        <div className="grid w-full grid-cols-12 gap-x-4">
          <img
            className="col-span-4"
            alt={live.title}
            src={`${baseUrl}/image/thumbnail/${live.imageLandscape?.path}`}
          />
          <span className="col-span-8">{live?.title}</span>
        </div>
      </div>
    </CommandItem>
  )
}

export default SearchItemLive
