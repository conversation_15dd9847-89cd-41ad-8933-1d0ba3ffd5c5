import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Rupee } from "@/components/common"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { baseWebUrl, graphqlClient } from "@/lib/utils"
import { useNavigate, useSearchParams } from "react-router"
import { useRazorpay, useUser } from "@/lib/hooks"
import { SUBSCRIPTION_PLANS } from "@/lib/graphql/queries"
import { SUBSCRIBE_TO_PLAN } from "@/lib/graphql/mutation"
import { useMutation, useQuery } from "@tanstack/react-query"
import { toast } from "@/components/ui/use-toast"

const Subscription = () => {
  const navigate = useNavigate()
  const { data, isLoading, isError } = useQuery({
    queryKey: ["subscription-plans"],
    queryFn: async () => graphqlClient.request(SUBSCRIPTION_PLANS),
  })

  const [searchParams] = useSearchParams()

  const { data: user } = useUser()

  const { handleRazorpay } = useRazorpay()

  const subscribeToPlan = useMutation({
    mutationFn: async (planId: string) => {
      return await graphqlClient.request(SUBSCRIBE_TO_PLAN, {
        redirectUrl: baseWebUrl + "/payment-success",
        planId: planId,
      })
    },
    onSuccess: (data) => {
      if (data.subscribeToPlan?.__typename === "PhonePePaymentResponse") {
        window.location.href = baseWebUrl + data.subscribeToPlan.goto_url
      } else if (
        data.subscribeToPlan?.__typename === "RazorPayPaymentResponse"
      ) {
        const price = data.subscribeToPlan.price
        const orderId = data.subscribeToPlan.order_id
        const ref = searchParams.get("ref")
        handleRazorpay({
          orderId: orderId,
          price: price,
          user: user?.getMe,
          gotoUrl: ref,
        })
      }
    },
    onError: (error) => {
      toast({
        description: error.message,
        variant: "destructive",
      })
    },
  })

  const handleSubscription = (planId: string) => {
    subscribeToPlan.mutate(planId)
  }

  if (isLoading) {
    return <PageLoader />
  }

  if (isError) {
    return <PageError />
  }

  return (
    <div className="mx-auto flex h-full min-h-screen max-w-4xl flex-col items-center justify-center">
      <h1 className="mb-4 w-full text-2xl font-bold">Subscribe to a plan</h1>
      <div className="grid w-full grid-cols-12 gap-8">
        {data?.subscriptionPlans?.map((item) => {
          return (
            item && (
              <Card key={item.id} className="col-span-4 bg-white text-black">
                <CardHeader>
                  <CardTitle className="text-center">{item.name}</CardTitle>
                  <CardDescription className="text-center text-xl">
                    {item.duration > 1
                      ? `${item.duration} Months`
                      : `${item.duration} Month`}
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center text-2xl font-bold">
                  <Rupee amount={item.price} />
                </CardContent>
                <CardFooter>
                  <Button
                    isLoading={subscribeToPlan.isPending}
                    onClick={() => handleSubscription(item.id)}
                    className="w-full"
                  >
                    Subscribe
                  </Button>
                </CardFooter>
              </Card>
            )
          )
        })}
      </div>
      <Button onClick={() => navigate(-1)} variant="secondary" className="mt-4">
        Go back
      </Button>
    </div>
  )
}

export default Subscription
