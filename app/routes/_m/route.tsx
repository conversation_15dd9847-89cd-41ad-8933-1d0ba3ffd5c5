import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/common"
import { useUser } from "@/lib/hooks"
import { useGlobalLoadingStore } from "@/lib/store"
import { cn } from "@/lib/utils/cn"
import { Link, NavLink, Outlet, useLoaderData, useNavigate } from "react-router"
import { useEffect } from "react"
import { UAParser } from "ua-parser-js"

import type { Route } from "../_m/+types/route"

export const meta = ({ location }: Route.MetaArgs) => {
  return [
    { title: "Enila" },
    {
      name: "description",
      content:
        "Enila is a user friendly streaming platform made with love in Aizawl, Mizoram",
    },
    {
      property: "og:title",
      content: `Enila`,
    },
    {
      property: "og:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      property: "og:image",
      itemProp: "image",
      content: "/enila_logo.png",
    },
    {
      property: "og:image:height",
      content: "600",
    },
    {
      property: "og:image:width",
      content: "600",
    },
    {
      property: "robots",
      content: "index,follow",
    },
    {
      property: "og:description",
      content:
        "Enila is a user friendly streaming platform made with love in Aizawl, Mizoram",
    },
    {
      property: "twitter:card",
      content: "summary_large_image",
    },
    {
      property: "twitter:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "twitter:title",
      content: "Enila",
    },
    {
      property: "twitter:description",
      content:
        "Enila is a user friendly streaming platform made with love in Aizawl, Mizoram",
    },
    {
      property: "twitter:image",
      content: "/enila_logo.png",
    },
    {
      property: "og:site_name",
      content: "Enila",
    },
  ]
}

export const loader = ({ request }: Route.LoaderArgs) => {
  const userAgent = request.headers.get("User-Agent") || undefined
  const parser = new UAParser(userAgent)
  const deviceType = parser.getDevice()

  return { deviceType }
}

export default function Index() {
  const { deviceType } = useLoaderData<typeof loader>()
  const navigate = useNavigate()
  const { data, isLoading, isError } = useUser()

  const { globalLoading } = useGlobalLoadingStore()

  useEffect(() => {
    if (
      deviceType.type !== "mobile" &&
      deviceType.type !== "tablet" &&
      window.innerWidth >= 720
    ) {
      navigate("/")
    }
  }, [deviceType.type, navigate])

  useEffect(() => {
    if (data?.getMe === null) {
      navigate("/")
    }
  }, [data, navigate])

  if (isLoading || globalLoading) {
    return <PageLoader />
  }

  if (isError) {
    return <PageError />
  }

  return (
    <div className="flex h-full min-h-screen flex-col">
      <header>
        <div className="z-50 flex h-[10vh] w-full items-center justify-center px-2 xl:hidden">
          <Link
            to="/"
            className="flex items-center justify-center gap-x-2 text-4xl font-bold text-theme-yellow"
          >
            <img
              alt="enila logo"
              className="mt-1 size-12"
              src="/enila_logo.png"
            />
            EN ILA
          </Link>
        </div>
      </header>
      <main className="mx-auto size-full grow px-4 xl:hidden">
        <Outlet />
      </main>
      <nav className="fixed bottom-0 flex w-full justify-around rounded-lg bg-theme-yellow py-2 text-sm text-black">
        <NavLink
          to="/m"
          className={({ isActive }) =>
            cn("col-span-1 text-center", {
              "font-bold": isActive,
            })
          }
          end
        >
          Movies
        </NavLink>
        <NavLink
          to="/m/shows"
          className={({ isActive }) =>
            cn("col-span-1 text-center", {
              "font-bold": isActive,
            })
          }
        >
          Shows
        </NavLink>
        <NavLink
          to="/m/live"
          className={({ isActive }) =>
            cn("col-span-1 text-center", {
              "font-bold": isActive,
            })
          }
        >
          Live
        </NavLink>
        <NavLink
          to="/m/subscription"
          className={({ isActive }) =>
            cn("col-span-1 text-center", {
              "font-bold": isActive,
            })
          }
        >
          Subscription
        </NavLink>
      </nav>
      <Footer />
    </div>
  )
}
