import { Button, buttonVariants } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON><PERSON>rigger,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import { graphql } from "@/gql"
import { graphqlClient } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"
import { DialogDescription } from "@radix-ui/react-dialog"
import { useMutation } from "@tanstack/react-query"
import { ClientError } from "graphql-request"
import { useState } from "react"

const DELETE_ACCOUNT = graphql(`
  mutation DeleteAccount {
    deleteMyAccount {
      message
    }
  }
`)

const DeleteAccount = () => {
  const [open, setOpen] = useState(false)
  const deleteAccount = useMutation({
    mutationFn: async () => {
      return await graphqlClient.request(DELETE_ACCOUNT)
    },
    onSuccess: () => {
      window.location.href = "/"
    },
    onError: (error) => {
      if (error instanceof ClientError) {
        toast({
          description:
            error?.response?.errors?.[0]?.message || "Unable to delete account",
        })
      } else {
        toast({
          description: "Unable to delete account",
        })
      }
    },
  })

  const handleDelete = () => {
    deleteAccount.mutate()
  }

  return (
    <>
      <div className="my-8">
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger
            className={cn(buttonVariants({ variant: "destructive" }))}
          >
            Delete Account
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>Delete Account</DialogHeader>
            <DialogDescription>
              Are you sure you want to delete your account?
            </DialogDescription>
            <DialogFooter className="gap-x-8">
              <Button
                onClick={handleDelete}
                isLoading={deleteAccount.isPending}
                variant="destructive"
              >
                Confirm
              </Button>
              <Button variant="secondary" onClick={() => setOpen(false)}>
                Cancel
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </>
  )
}

export default DeleteAccount
