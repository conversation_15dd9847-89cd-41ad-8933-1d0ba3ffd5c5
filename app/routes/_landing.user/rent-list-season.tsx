import { type FragmentType, graphql, useFragment } from "@/gql"
import dayjs from "dayjs"

const RENT_LIST_SEASON = graphql(`
  fragment RentListSeasonFragment on TVShowSeason {
    id
    title
    tvShow {
      title
    }
  }
`)

interface Props {
  data: FragmentType<typeof RENT_LIST_SEASON>
  price?: number
  paymentStatus?: string
  endDate?: string
}

const RentListSeason = ({ data, price, paymentStatus, endDate }: Props) => {
  const season = useFragment(RENT_LIST_SEASON, data)

  return (
    <li className="my-4 flex flex-col border-2 p-4">
      <div>
        {season.tvShow?.title} {season.title}
      </div>
      <div className="flex gap-x-8">
        {price && <span>Price: {price}</span>}
        {paymentStatus && <span>Payment status: {paymentStatus}</span>}
        {endDate && (
          <span>End date: {dayjs(endDate).format("Do MMMM YYYY")}</span>
        )}
      </div>
    </li>
  )
}

export default RentListSeason
