import { type FragmentType, graphql, useFragment } from "@/gql"
import dayjs from "dayjs"

const RENT_LIST_EPISODE = graphql(`
  fragment RentListEpisodeFragment on TVShowEpisodes {
    id
    title
    season {
      title
    }
    tvShow {
      title
    }
  }
`)

interface Props {
  data: FragmentType<typeof RENT_LIST_EPISODE>
  price?: number
  paymentStatus?: string
  endDate?: string
}

const RentListEpisode = ({ data, price, paymentStatus, endDate }: Props) => {
  const episode = useFragment(RENT_LIST_EPISODE, data)

  return (
    <li className="my-4 flex flex-col border-2 p-4">
      <div>
        {episode.tvShow?.title} - {episode.season?.title} - {episode.title}
      </div>
      <div className="flex gap-x-8">
        {price && <span>Price: {price}</span>}
        {paymentStatus && <span>Payment status: {paymentStatus}</span>}
        {endDate && (
          <span>End date: {dayjs(endDate).format("Do MMMM YYYY")}</span>
        )}
      </div>
    </li>
  )
}

export default RentListEpisode
