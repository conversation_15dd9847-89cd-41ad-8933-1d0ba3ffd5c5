import { type FragmentType, graphql, useFragment } from "@/gql"
import dayjs from "dayjs"

const RENT_LIST_MOVIE = graphql(`
  fragment RentListMovieFragment on Movie {
    id
    title
  }
`)

interface Props {
  data: FragmentType<typeof RENT_LIST_MOVIE>
  price?: number
  paymentStatus?: string
  endDate?: string
}

const RentLisMovie = ({ data, price, paymentStatus, endDate }: Props) => {
  const movie = useFragment(RENT_LIST_MOVIE, data)

  return (
    <li className="my-4 flex flex-col border-2 p-4">
      <div>{movie.title}</div>
      <div className="flex gap-x-8">
        {price && <span>Price: {price}</span>}
        {paymentStatus && <span>Payment status: {paymentStatus}</span>}
        {endDate && (
          <span>End date: {dayjs(endDate).format("Do MMMM YYYY")}</span>
        )}
      </div>
    </li>
  )
}

export default RentLisMovie
