import { graphql } from "@/gql"
import { graphqlClient } from "@/lib/utils"
import RentListEpisode from "./rent-list-episode"
import RentListLive from "./rent-list-live"
import RentListMovie from "./rent-list-movie"
import RentListSeason from "./rent-list-season"
import { useSearchParams } from "react-router"
import { useEffect, useState } from "react"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
} from "@/components/ui/pagination"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils/cn"
import { LoadingIcon } from "@/components/icons"
import { useQuery } from "@tanstack/react-query"

const USER_RENT_LIST = graphql(`
  query MyContentRentList($page: Int, $first: Int!) {
    myContentRentList(first: $first, page: $page) {
      data {
        id
        status
        price
        start_date
        end_date
        rentable {
          __typename
          ...RentListEpisodeFragment
          ...RentListLiveFragment
          ...RentListMovieFragment
          ...RentListSeasonFragment
        }
      }
      paginatorInfo {
        lastPage
        total
      }
    }
  }
`)

const UserRentList = () => {
  const [searchParams, setSearchParams] = useSearchParams()

  const [page, setPage] = useState(parseInt(searchParams.get("page") || "1"))

  const { data, isLoading, isError } = useQuery({
    queryKey: ["user-rent-list"],
    queryFn: async () =>
      graphqlClient.request(USER_RENT_LIST, {
        first: 10,
        page: page,
      }),
    enabled: !!page,
  })

  const handlePageChange = (page: number) => {
    setPage(page)
    setSearchParams(
      (params) => {
        params.set("page", page.toString())
        return params
      },
      {
        preventScrollReset: true,
      }
    )
  }

  useEffect(() => {
    if (!page) {
      setSearchParams(
        (params) => {
          params.set("page", "1")
          return params
        },
        {
          preventScrollReset: true,
        }
      )
    }
  }, [page, setSearchParams])

  if (isLoading) {
    return (
      <div className="flex w-full justify-center">
        <LoadingIcon className="size-8 animate-spin" />
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex w-full justify-center">
        <p className="text-xl">Error fetching rent list.</p>
      </div>
    )
  }

  const pagination = data?.myContentRentList?.paginatorInfo

  return (
    <>
      <div className="flex w-full flex-col">
        <div className="text-2xl font-bold">My rent list</div>
        <ul className="list-inside list-disc">
          {data?.myContentRentList.data.map((rent) => {
            if (rent.rentable?.__typename === "TVShowEpisodes")
              return (
                <RentListEpisode
                  key={rent.id}
                  data={rent.rentable}
                  price={rent.price}
                  paymentStatus={rent.status}
                  endDate={rent.end_date}
                />
              )
            else if (rent.rentable?.__typename === "Live") {
              return (
                <RentListLive
                  key={rent.id}
                  data={rent.rentable}
                  price={rent.price}
                  paymentStatus={rent.status}
                  endDate={rent.end_date}
                />
              )
            } else if (rent.rentable?.__typename === "Movie") {
              return (
                <RentListMovie
                  key={rent.id}
                  data={rent.rentable}
                  price={rent.price}
                  paymentStatus={rent.status}
                  endDate={rent.end_date}
                />
              )
            } else if (rent.rentable?.__typename === "TVShowSeason") {
              return (
                <RentListSeason
                  key={rent.id}
                  data={rent.rentable}
                  price={rent.price}
                  paymentStatus={rent.status}
                  endDate={rent.end_date}
                />
              )
            }
          })}
        </ul>
      </div>

      {pagination && pagination?.lastPage > 1 && (
        <Pagination className="mt-8">
          <PaginationContent>
            {Array.from({ length: pagination.lastPage }, (_, i) => i + 1).map(
              (item) => {
                if (
                  Math.abs(item - page) <= 1 ||
                  item === 1 ||
                  item === pagination.lastPage ||
                  (item % 3 === 0 && Math.abs(item - page) <= 2)
                ) {
                  return (
                    <PaginationItem key={item}>
                      <Button
                        onClick={() => handlePageChange(item)}
                        variant="outline"
                        className={cn(
                          {
                            "bg-red-500 text-white hover:bg-red-500/90":
                              item === page,
                          },
                          {
                            "bg-white text-black hover:bg-white/90":
                              item !== page,
                          }
                        )}
                      >
                        {item}
                      </Button>
                    </PaginationItem>
                  )
                } else if (
                  item === 2 ||
                  item === pagination.lastPage - 1 ||
                  (item % 3 === 1 && Math.abs(item - page) < 2)
                ) {
                  return <PaginationEllipsis key={item} />
                }
              }
            )}
          </PaginationContent>
        </Pagination>
      )}
    </>
  )
}

export default UserRentList
