import { useUser } from "@/lib/hooks"
import { SUPPORT_NUMBER } from "@/lib/utils/constants"
import { Link } from "react-router";
import dayjs from "dayjs"
import UserRentList from "./user-rent-list"
import { PageError, PageLoader } from "@/components/common"
import DeleteAccount from "./delete-account"

const User = () => {
  const { data, isLoading, isError } = useUser()

  if (isLoading) {
    return <PageLoader />
  }

  if (isError) {
    return <PageError />
  }
  const userSubscription = data?.getMe?.subscription

  return (
    <section className="mx-auto flex w-full max-w-6xl flex-col pt-[10vh]">
      <h1 className="text-4xl font-bold">Account</h1>
      <h2 className="mt-4 text-xl">
        {data?.getMe?.mobile_number ? (
          <>Mobile Number: {data.getMe.mobile_number}</>
        ) : (
          <>Email: {data?.getMe?.email}</>
        )}
      </h2>
      <div className="my-4 border" />
      <p className="text-2xl font-bold">
        Subscription Status (
        {data?.getMe?.subscription?.subscription_status || "Not a subscriber"})
        {data?.getMe?.subscription?.subscription_status === "expired" ? (
          <Link className="text-base underline" to="/subscription">
            Renew subscription
          </Link>
        ) : null}
      </p>

      {userSubscription?.subscription_status === "active" ? (
        <div className="mt-4 flex w-full flex-col">
          <div className="grid w-full grid-cols-12">
            <div className="col-span-2">Plan</div>
            <div className="col-span-10">{userSubscription.plan?.name}</div>
          </div>
          <div className="grid w-full grid-cols-12">
            <div className="col-span-2">Duration</div>
            <div className="col-span-10">
              {userSubscription.plan?.duration} Month(s)
            </div>
          </div>
          <div className="grid w-full grid-cols-12">
            <div className="col-span-2">Ends</div>
            <div className="col-span-10">
              {dayjs(userSubscription.end_date).format("Do MMMM YYYY")}
            </div>
          </div>
        </div>
      ) : userSubscription?.subscription_status === "initiated" ? (
        <div className="mt-4 text-lg">
          <p>
            You are currently not subscribed to any plan. If your account has
            been debited, please wait for a few minutes before refreshing this
            page. If you think this is a mistake, please contact support at{" "}
            {SUPPORT_NUMBER}.
          </p>

          <p className="text-sm">
            If your account has not been debited, you can safely{" "}
            <a className="underline" href="/subscription">
              restart your subscription.
            </a>
          </p>
        </div>
      ) : (
        <>
          <div className="mt-4 text-lg">
            You are currently not subscribed to any plans.
          </div>
          <a className="underline" href="/subscription">
            Subscribe now to enjoy premium content.
          </a>
        </>
      )}

      <div className="my-4 border" />

      <UserRentList />
      <DeleteAccount />
    </section>
  )
}

export default User
