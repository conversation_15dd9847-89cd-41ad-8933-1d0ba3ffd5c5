import {
  Carousel,
  Carousel<PERSON>ontent,
  Carousel<PERSON>tem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import { type FragmentType, useFragment } from "@/gql"
import OtherCategoryMovie from "./other-category-movie"
import OtherCategoryTvShow from "./other-category-tv-show"
import OtherCategoryLiveShow from "./other-category-live-show"
import { HOME_DATA_FRAGMENT } from "@/lib/graphql/fragments"
import { cn } from "@/lib/utils/cn"

interface Props {
  otherCategories: Array<FragmentType<typeof HOME_DATA_FRAGMENT>>
}

const OtherCategories = ({ otherCategories }: Props) => {
  const data = useFragment(HOME_DATA_FRAGMENT, otherCategories)

  return (
    <>
      {data.map((item) => {
        return (
          <section key={item.id} className="relative my-2 overflow-x-hidden">
            <h2 className="px-8 py-2 text-2xl font-bold">{item.category}</h2>
            <Carousel>
              <CarouselContent>
                {item?.homeDataList?.map((card) => {
                  return (
                    card && (
                      <CarouselItem
                        className={cn("basis-1/4", {
                          "basis-[16%]": item.display_type === "portrait",
                          // "basis-1/4": item.display_type === "portrait2x",
                          "basis-1/4": item.display_type === "square2x" || item.display_type === "portrait2x",
                          "basis-[14%]": item.display_type === "square",
                        })}
                        key={card.id}
                      >
                        {card?.homeable?.__typename === "Movie" && (
                          <div className="relative px-4">
                            <OtherCategoryMovie
                              movie={card.homeable}
                              displayType={item.display_type || ""}
                            />
                          </div>
                        )}
                        {card?.homeable?.__typename === "TVShow" && (
                          <div className="relative px-4">
                            <OtherCategoryTvShow
                              tvShow={card.homeable}
                              displayType={item.display_type || ""}
                            />
                          </div>
                        )}
                        {card?.homeable?.__typename === "Live" && (
                          <div className="relative px-4">
                            <OtherCategoryLiveShow
                              liveShow={card.homeable}
                              displayType={item.display_type || ""}
                            />
                          </div>
                        )}
                      </CarouselItem>
                    )
                  )
                })}
              </CarouselContent>
              {item?.homeDataList &&
                item.display_type !== "portrait2x" &&
                item.display_type !== "square2x" &&
                item.homeDataList.length > 5 && <CarouselControls />}
              {item?.homeDataList &&
                item.display_type === "portrait2x" &&
                item.homeDataList.length > 3 && <CarouselControls />}
              {item?.homeDataList &&
                item.display_type === "square2x" &&
                item.homeDataList.length > 4 && (
                  <>
                    <CarouselControls />
                  </>
                )}
            </Carousel>
          </section>
        )
      })}
    </>
  )
}

const CarouselControls = () => {
  return (
    <>
      <div className="absolute left-0 top-0 flex h-full w-8 items-center justify-between">
        <CarouselPrevious className="h-full w-8 rounded-none border-0" />
      </div>
      <div className="absolute right-0 top-0 flex h-full w-8 items-center justify-between">
        <CarouselNext className="h-full w-8 rounded-none border-0 border-none" />
      </div>
    </>
  )
}

export default OtherCategories
