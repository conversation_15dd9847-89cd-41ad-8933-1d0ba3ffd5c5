import { PlayIcon } from "@/components/icons"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { type FragmentType, useFragment } from "@/gql"
import { TvShowFragment } from "@/lib/graphql/fragments"
import { useLanguage } from "@/lib/store"
import { baseUrl } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"
import { useState } from "react"
import { Link } from "react-router"
import { useDebounce } from "use-debounce"
import CategoryItem from "./category-item"
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card"

interface Props {
  tvShow: FragmentType<typeof TvShowFragment>
  displayType: string
}

const OtherCategoryTvShow = ({ tvShow, displayType }: Props) => {
  const [isOverButton, setIsOverButton] = useState(false)
  const [isOverPopover, setIsOverPopover] = useState(false)

  const data = useFragment(TvShowFragment, tvShow)
  const { language } = useLanguage()

  const [isOverAny] = useDebounce(isOverButton || isOverPopover, 100)

  return (
    <HoverCard openDelay={100} closeDelay={100}>
      <HoverCardTrigger asChild>
        <Link
          to={`/show/${data.slug}`}
          className={cn(
            "size-full rounded-none p-0 transition duration-300 ease-in"
          )}
          onMouseEnter={() => {
            setIsOverButton(true)
          }}
          onMouseLeave={() => {
            setIsOverButton(false)
          }}
        >
          {displayType === "landscape" && (
            <AspectRatio className="overflow-hidden" ratio={16 / 9}>
              <CategoryItem
                shouldTransition={isOverAny}
                alt={data.title}
                blurhash={data.imageLandscape?.hash || ""}
                src={data.imageLandscape?.path || ""}
              />
            </AspectRatio>
          )}
          {displayType === "portrait" && (
            <AspectRatio className="overflow-hidden" ratio={3 / 4}>
              <CategoryItem
                shouldTransition={isOverAny}
                alt={data.title}
                blurhash={data.imagePortrait?.hash || ""}
                src={data.imagePortrait?.path || ""}
              />
            </AspectRatio>
          )}
          {displayType === "square" && (
            <AspectRatio className="overflow-hidden" ratio={1 / 1}>
              <CategoryItem
                shouldTransition={isOverAny}
                alt={data.title}
                blurhash={data.imageSquare?.hash || ""}
                src={data.imageSquare?.path || ""}
              />
            </AspectRatio>
          )}
          {displayType === "square2x" && (
            <AspectRatio className="overflow-hidden" ratio={1 / 1}>
              <CategoryItem
                shouldTransition={isOverAny}
                alt={data.title}
                blurhash={data.imageSquare2x?.hash || ""}
                src={data.imageSquare2x?.path || ""}
              />
            </AspectRatio>
          )}
          {displayType === "portrait2x" && (
            <AspectRatio className="overflow-hidden" ratio={3 / 4}>
              <CategoryItem
                shouldTransition={isOverAny}
                alt={data.title}
                blurhash={data.imagePortrait2x?.hash || ""}
                src={data.imagePortrait2x?.path || ""}
              />
            </AspectRatio>
          )}
        </Link>
      </HoverCardTrigger>

      <HoverCardContent
        side="right"
        className="w-[350px] overflow-hidden bg-popover/90 px-0"
        sideOffset={-200}
        onMouseLeave={() => {
          setIsOverPopover(false)
        }}
        onMouseEnter={() => {
          setIsOverPopover(true)
        }}
      >
        <div>
          <AspectRatio ratio={16 / 9}>
            <Link className="relative" to={`/show/${data.slug}`}>
              <img
                className="size-full object-contain"
                alt={data.title}
                src={`${baseUrl}/image/medium/${data.imageLandscape?.path}`}
              />
              <div className="absolute inset-0 flex size-full items-center justify-center bg-black/55 duration-300 hover:scale-105 hover:bg-black/75">
                <PlayIcon size={128} />
              </div>
            </Link>
          </AspectRatio>
          <div className="space-y-2 p-2">
            <div>{data.title}</div>
            <div>
              <div className="flex font-semibold">
                <span className="border-r-2 border-white pr-2">
                  {data?.production_year}
                </span>
                <span className="px-2">{data?.age_label || ""}</span>
              </div>
            </div>
            <div className="line-clamp-4 whitespace-pre-line">
              {language === "mz" ? data.description_mz : data.description_en}
            </div>
          </div>
        </div>
      </HoverCardContent>
    </HoverCard>
  )
}

export default OtherCategoryTvShow
