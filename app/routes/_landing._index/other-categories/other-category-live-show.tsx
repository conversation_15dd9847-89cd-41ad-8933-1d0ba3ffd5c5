import { BlurhashImage } from "@/components/common"
import { PlayIcon } from "@/components/icons"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { Button, buttonVariants } from "@/components/ui/button"
import { type FragmentType, useFragment } from "@/gql"
import { LIVE_SHOW_FRAGMENT } from "@/lib/graphql/fragments"
import { useLiveRentDialogStore } from "@/lib/store"
import { baseUrl } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"
import { Link } from "react-router"

interface Props {
  liveShow: FragmentType<typeof LIVE_SHOW_FRAGMENT>
  displayType: string
}

const OtherCategoryLiveShow = ({ liveShow, displayType }: Props) => {
  const data = useFragment(LIVE_SHOW_FRAGMENT, liveShow)

  const { openLive } = useLiveRentDialogStore()

  const openRentDialog = () => {
    openLive(data.id, data.price)
  }

  return (
    <>
      {displayType === "landscape" && (
        <AspectRatio className="overflow-hidden" ratio={16 / 9}>
          {data.is_rented || data.is_free ? (
            <Link
              to={`/live/${data.slug}`}
              className={cn(
                buttonVariants({ variant: "link" }),
                "relative size-full rounded-none p-0 transition duration-300 ease-in hover:scale-110"
              )}
            >
              <div className="absolute inset-0 z-10 bg-transparent opacity-0 transition duration-300 ease-out hover:bg-black/75 hover:opacity-100">
                <div className="flex size-full items-center justify-center opacity-0 transition duration-1000 ease-in-out hover:scale-105 hover:opacity-100">
                  <PlayIcon size={128} />
                </div>
              </div>
              <BlurhashImage
                alt={data.title}
                src={`${baseUrl}/image/medium/${data.imageLandscape?.path}`}
                blurhash={data.imageLandscape?.hash || ""}
              />
            </Link>
          ) : data.for_subscriber ? (
            <Link
              to={`/subscription?ref=${window.location.pathname}${window.location.search}`}
              className={cn(
                buttonVariants({ variant: "link" }),
                "relative size-full rounded-none p-0 transition duration-300 ease-in hover:scale-110"
              )}
            >
              <div className="absolute inset-0 z-10 bg-transparent opacity-0 transition duration-300 ease-out hover:bg-black/75 hover:opacity-100">
                <div className="flex size-full items-center justify-center opacity-0 transition duration-1000 ease-in-out hover:scale-105 hover:opacity-100">
                  <PlayIcon size={128} />
                </div>
              </div>
              <BlurhashImage
                alt={data.title}
                src={`${baseUrl}/image/medium/${data.imageLandscape?.path}`}
                blurhash={data.imageLandscape?.hash || ""}
              />
            </Link>
          ) : (
            <Button
              onClick={openRentDialog}
              className="relative size-full rounded-none p-0 transition duration-300 ease-in hover:scale-110"
              variant="link"
            >
              <div className="absolute inset-0 z-10 bg-transparent opacity-0 transition duration-300 ease-out hover:bg-black/75 hover:opacity-100">
                <div className="flex size-full items-center justify-center opacity-0 transition duration-1000 ease-in-out hover:scale-105 hover:opacity-100">
                  <PlayIcon size={128} />
                </div>
              </div>
              <BlurhashImage
                alt={data.title}
                src={`${baseUrl}/image/medium/${data.imageLandscape?.path}`}
                blurhash={data.imageLandscape?.hash || ""}
              />
            </Button>
          )}
        </AspectRatio>
      )}
      {displayType === "portrait" && (
        <AspectRatio className="overflow-hidden" ratio={3 / 4}>
          {data.is_rented || data.is_free ? (
            <Link
              to={`/live/${data.slug}`}
              className="relative size-full rounded-none p-0 transition duration-300 ease-in hover:scale-110"
            >
              <div className="absolute inset-0 z-10 bg-transparent opacity-0 transition duration-300 ease-out hover:bg-black/75 hover:opacity-100">
                <div className="flex size-full items-center justify-center opacity-0 transition duration-1000 ease-in-out hover:scale-105 hover:opacity-100">
                  <PlayIcon size={128} />
                </div>
              </div>
              <BlurhashImage
                alt={data.title}
                src={`${baseUrl}/image/medium/${data.imagePortrait?.path}`}
                blurhash={data.imagePortrait?.hash || ""}
              />
            </Link>
          ) : data.for_subscriber ? (
            <Link
              to={`/subscription?ref=${window.location.pathname}${window.location.search}`}
              className="relative size-full rounded-none p-0 transition duration-300 ease-in hover:scale-110"
            >
              <div className="absolute inset-0 z-10 bg-transparent opacity-0 transition duration-300 ease-out hover:bg-black/75 hover:opacity-100">
                <div className="flex size-full items-center justify-center opacity-0 transition duration-1000 ease-in-out hover:scale-105 hover:opacity-100">
                  <PlayIcon size={128} />
                </div>
              </div>
              <BlurhashImage
                alt={data.title}
                src={`${baseUrl}/image/medium/${data.imagePortrait?.path}`}
                blurhash={data.imagePortrait?.hash || ""}
              />
            </Link>
          ) : (
            <Button
              onClick={openRentDialog}
              className="relative size-full rounded-none p-0 transition duration-300 ease-in hover:scale-110"
              variant="link"
            >
              <div className="absolute inset-0 z-10 bg-transparent opacity-0 transition duration-300 ease-out hover:bg-black/75 hover:opacity-100">
                <div className="flex size-full items-center justify-center opacity-0 transition duration-1000 ease-in-out hover:scale-105 hover:opacity-100">
                  <PlayIcon size={128} />
                </div>
              </div>
              <BlurhashImage
                alt={data.title}
                src={`${baseUrl}/image/medium/${data.imagePortrait?.path}`}
                blurhash={data.imagePortrait?.hash || ""}
              />
            </Button>
          )}
        </AspectRatio>
      )}
    </>
  )
}

export default OtherCategoryLiveShow
