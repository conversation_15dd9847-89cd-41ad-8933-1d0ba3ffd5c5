import { FavoriteIcon, LoadingIcon, PlayIcon } from "@/components/icons"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { Button } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"
import { type FragmentType, useFragment } from "@/gql"
import { WatchListableType } from "@/gql/graphql"
import { MovieFragment } from "@/lib/graphql/fragments"
import { MOVIE_BY_ID_QUERY } from "@/lib/graphql/queries"
import { useWatchList } from "@/lib/hooks"
import { useLanguage } from "@/lib/store"
import { baseUrl, formatDuration, graphqlClient } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { useState } from "react"
import { Link } from "react-router"
import { useDebounce } from "use-debounce"
import CategoryItem from "./category-item"
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card"

interface Props {
  movie: FragmentType<typeof MovieFragment>
  displayType: string
}

const OtherCategoryMovie = ({ movie, displayType }: Props) => {
  const [isOverButton, setIsOverButton] = useState(false)
  const [isOverPopover, setIsOverPopover] = useState(false)

  const movieData = useFragment(MovieFragment, movie)
  const { language } = useLanguage()

  const queryClient = useQueryClient()

  const { data, isLoading, isError } = useQuery({
    queryKey: ["movie-by-id", movieData.id],
    queryFn: async () =>
      graphqlClient.request(MOVIE_BY_ID_QUERY, {
        id: movieData.id,
        enabled: !!movieData.id,
      }),
  })

  const { addToWatchList, deleteFromWatchList } = useWatchList()

  const [isOverAny] = useDebounce(isOverButton || isOverPopover, 100)

  const handleAddToWatchList = () => {
    const id = data?.movieById?.id

    const movieTitle = data?.movieById?.title
    if (id) {
      addToWatchList.mutate(
        {
          id: id,
          contentType: WatchListableType.Movie,
        },
        {
          onSuccess: () => {
            void queryClient.refetchQueries({
              queryKey: ["movie-by-id"],
            })
            toast({
              description: `${movieTitle} added to watch list`,
              variant: "default",
            })
          },
        }
      )
    }
  }

  const handleDeleteFromWatchList = () => {
    const id = data?.movieById?.isAddedToWatchList
    const movieTitle = data?.movieById?.title
    if (id) {
      deleteFromWatchList.mutate(
        {
          id: id.toString(),
        },
        {
          onSuccess: () => {
            void queryClient.refetchQueries({
              queryKey: ["movie-by-id"],
            })
            toast({
              description: `${movieTitle} removed from watchlist`,
              variant: "default",
            })
          },
        }
      )
    }
  }

  return (
    <HoverCard openDelay={100} closeDelay={100}>
      <HoverCardTrigger asChild>
        <Link
          to={`/movie/${movieData.slug}`}
          className={cn(
            "relative size-full rounded-none p-0 transition duration-300 ease-in"
          )}
          onMouseEnter={() => {
            setIsOverButton(true)
          }}
          onMouseLeave={() => {
            setIsOverButton(false)
          }}
        >
          {displayType === "landscape" && (
            <AspectRatio className="overflow-hidden" ratio={16 / 9}>
              <CategoryItem
                shouldTransition={isOverAny}
                alt={movieData.title}
                blurhash={movieData.imageLandscape?.hash || ""}
                src={movieData.imageLandscape?.path || ""}
              />
            </AspectRatio>
          )}
          {displayType === "portrait" && (
            <AspectRatio className="overflow-hidden" ratio={3 / 4}>
              <CategoryItem
                shouldTransition={isOverAny}
                alt={movieData.title}
                blurhash={movieData.imagePortrait?.hash || ""}
                src={movieData.imagePortrait?.path || ""}
              />
            </AspectRatio>
          )}
          {displayType === "square" && (
            <AspectRatio className="overflow-hidden" ratio={1 / 1}>
              <CategoryItem
                shouldTransition={isOverAny}
                alt={movieData.title}
                blurhash={movieData.imageSquare?.hash || ""}
                src={movieData.imageSquare?.path || ""}
              />
            </AspectRatio>
          )}
          {displayType === "square2x" && (
            <AspectRatio className="overflow-hidden" ratio={1 / 1}>
              <CategoryItem
                shouldTransition={isOverAny}
                alt={movieData.title}
                blurhash={movieData.imageSquare2x?.hash || ""}
                src={movieData.imageSquare2x?.path || ""}
              />
            </AspectRatio>
          )}
          {displayType === "portrait2x" && (
            <AspectRatio className="overflow-hidden" ratio={3 / 4}>
              <CategoryItem
                shouldTransition={isOverAny}
                alt={movieData.title}
                blurhash={movieData.imagePortrait2x?.hash || ""}
                src={movieData.imagePortrait2x?.path || ""}
              />
            </AspectRatio>
          )}
        </Link>
      </HoverCardTrigger>

      <HoverCardContent
        side="right"
        sideOffset={-200}
        className="w-[350px] overflow-hidden bg-popover/90 px-0"
          onMouseEnter={() => {
            setIsOverPopover(true)
          }}
          onMouseLeave={() => {
            setIsOverPopover(false)
          }}
      >
        <div>
          <AspectRatio ratio={16 / 9}>
            <Link className="relative" to={`/movie/${movieData.slug}`}>
              <img
                className="size-full object-contain"
                alt={movieData.title}
                src={`${baseUrl}/image/medium/${movieData.imageLandscape?.path}`}
              />
              <div className="absolute inset-0 flex size-full items-center justify-center bg-black/55 duration-300 hover:scale-105 hover:bg-black/75">
                <PlayIcon size={128} />
              </div>
            </Link>
          </AspectRatio>
          <div className="space-y-2 p-2">
            <div>{movieData.title}</div>
            <div>
              <div className="flex font-semibold">
                <span className="border-r-2 border-white pr-2">
                  {movieData?.production_year}
                </span>
                {movieData?.duration > 0 ? (
                  <span className="border-r-2 border-white px-2">
                    {formatDuration(movieData.duration)}
                  </span>
                ) : null}
                <span className="px-2">{movieData?.age_label || ""}</span>
              </div>
            </div>
            <div className="line-clamp-4 whitespace-pre-line text-sm">
              {language === "mz"
                ? movieData.description_mz
                : movieData.description_en}
            </div>
          </div>
        </div>
        <div className="flex">
          {!isLoading && !isError && (
            <>
              {data?.movieById?.isAddedToWatchList ? (
                <Button
                  className="space-x-2 p-2"
                  onClick={handleDeleteFromWatchList}
                  variant="ghost"
                  disabled={deleteFromWatchList.isPending}
                >
                  {deleteFromWatchList.isPending ? (
                    <LoadingIcon />
                  ) : (
                    <FavoriteIcon fill={true} color="theme-yellow" />
                  )}
                </Button>
              ) : (
                <Button
                  className="space-x-2 p-2"
                  onClick={handleAddToWatchList}
                  variant="ghost"
                  disabled={addToWatchList.isPending}
                >
                  {addToWatchList.isPending ? (
                    <LoadingIcon />
                  ) : (
                    <FavoriteIcon />
                  )}
                </Button>
              )}
            </>
          )}
        </div>
      </HoverCardContent>
    </HoverCard>
  )
}

export default OtherCategoryMovie
