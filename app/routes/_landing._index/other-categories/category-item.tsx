import { BlurhashImage } from "@/components/common"
import { baseUrl } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"

interface Props {
  alt: string
  blurhash?: string
  src?: string
  shouldTransition: boolean
}

const CategoryItem = ({ alt, blurhash, src, shouldTransition }: Props) => {
  return (
    <>
      {src && src.length > 0 ? (
        <>
          <div
            className={cn(
              "absolute inset-0 z-10 bg-transparent opacity-0 transition duration-300 ease-out",
              {
                "bg-black/75 opacity-100": shouldTransition,
              }
            )}
          >
            {/* <div */}
            {/*   className={cn( */}
            {/*     "flex size-full items-center justify-center opacity-0 transition duration-1000 ease-in-out", */}
            {/*     { */}
            {/*       "scale-105 opacity-100": shouldTransition, */}
            {/*     } */}
            {/*   )} */}
            {/* > */}
            {/*   <PlayIcon size={128} /> */}
            {/* </div> */}
          </div>
          <BlurhashImage
            alt={alt}
            src={`${baseUrl}/image/medium/${src}`}
            blurhash={blurhash || ""}
          />
        </>
      ) : (
        <div className="flex size-full items-center justify-center bg-input/70 font-bold">
          Image not found
        </div>
      )}
    </>
  )
}

export default CategoryItem
