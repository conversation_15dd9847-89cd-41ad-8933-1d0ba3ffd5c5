import { BlurhashImage } from "@/components/common"
import { PlayIcon } from "@/components/icons"
import { Button } from "@/components/ui/button"
import { type FragmentType, useFragment } from "@/gql"
import { type AppImage } from "@/gql/graphql"
import { LIVE_SHOW_FRAGMENT } from "@/lib/graphql/fragments"
import { useLanguage, useLiveRentDialogStore } from "@/lib/store"
import { baseUrl } from "@/lib/utils"
import { Link } from "react-router"

interface Props {
  data: FragmentType<typeof LIVE_SHOW_FRAGMENT>
  titleImage?: AppImage
  posterImage?: AppImage
}

const SpotlightItemLiveShow = ({ data, titleImage, posterImage }: Props) => {
  const liveShow = useFragment(LIVE_SHOW_FRAGMENT, data)
  const { openLive } = useLiveRentDialogStore()
  const { language } = useLanguage()

  const openRentDialog = () => {
    openLive(liveShow.id, liveShow.price)
  }

  return (
    <>
      <div className="absolute top-1/3 z-10 flex size-full flex-col gap-y-4 px-8 text-white md:w-1/2 lg:bottom-1/3 lg:w-1/3">
        <div className="flex h-full max-h-56 items-end justify-center">
          {titleImage && (
            <img
              className="object-contain"
              alt={liveShow?.title}
              src={`${baseUrl}/image/small/${titleImage.path}`}
            />
          )}
        </div>
        <p className="description-scrollbar hidden h-24 overflow-y-auto pr-2 lg:block">
          {language === "en"
            ? liveShow?.description_en
            : liveShow?.description_mz}
        </p>
      </div>
      {posterImage && (
        <BlurhashImage
          className="h-[75vh] w-full object-cover"
          src={`${baseUrl}/image/web-landscape/${posterImage.path}`}
          alt={liveShow?.title}
          blurhash={posterImage?.hash || ""}
        />
      )}
      <div className="absolute inset-0 flex items-center justify-center">
        {liveShow.is_free || liveShow.is_rented ? (
          <Link
            to={`/live/${liveShow.slug}`}
            className="mx-auto flex h-4/5 w-2/3 items-center justify-center"
          >
            <PlayIcon size={100} />
          </Link>
        ) : liveShow.for_subscriber ? (
          <Link
            to={`/subscription?ref=${window.location.pathname}${window.location.search}`}
            className="mx-auto flex h-4/5 w-2/3 items-center justify-center"
          >
            <PlayIcon size={100} />
          </Link>
        ) : (
          <Button
            onClick={openRentDialog}
            variant="link"
            className="mx-auto flex h-4/5 w-2/3 items-center justify-center"
          >
            <PlayIcon size={100} />
          </Button>
        )}
      </div>
    </>
  )
}

export default SpotlightItemLiveShow
