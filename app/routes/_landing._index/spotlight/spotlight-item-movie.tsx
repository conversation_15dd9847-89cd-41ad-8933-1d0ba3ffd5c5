import { BlurhashImage } from "@/components/common"
import { PlayIcon } from "@/components/icons"
import { type FragmentType, useFragment } from "@/gql"
import { type AppImage } from "@/gql/graphql"
import { MovieFragment } from "@/lib/graphql/fragments"
import { useLanguage } from "@/lib/store"
import { baseUrl, formatDuration } from "@/lib/utils"
import { Link } from "react-router"

interface Props {
  data: FragmentType<typeof MovieFragment>
  titleImage?: AppImage
  posterImage?: AppImage
}

const SpotlightItemMovie = ({ data, titleImage, posterImage }: Props) => {
  const movie = useFragment(MovieFragment, data)
  const { language } = useLanguage()

  return (
    <>
      <div className="absolute top-1/3 z-10 flex size-full flex-col gap-y-4 px-8 text-white md:w-1/2 lg:bottom-1/3 lg:w-1/3">
        <div className="flex h-full max-h-56 items-end justify-center">
          {titleImage && (
            <img
              className="object-contain"
              alt={movie?.title}
              src={`${baseUrl}/image/small/${titleImage.path}`}
            />
          )}
        </div>
        <p className="description-scrollbar hidden h-24 overflow-y-auto pr-2 lg:block">
          {language === "en" ? movie?.description_en : movie.description_mz}
        </p>
        <div className="flex font-semibold">
          <span className="border-r-2 border-white pr-2">
            {movie?.production_year}
          </span>
          {movie?.duration > 0 ? (
            <span className="border-r-2 border-white px-2">
              {formatDuration(movie.duration)}
            </span>
          ) : null}
          <span className="px-2">{movie?.age_label || ""}</span>
        </div>
        <div>
          {movie?.genre ? (
            <div>
              <span className="font-bold">Genre: </span>
              {movie.genre}
            </div>
          ) : null}
        </div>
      </div>
      {posterImage && (
        <BlurhashImage
          className="h-[75vh] w-full object-cover"
          src={`${baseUrl}/image/web-landscape/${posterImage.path}`}
          alt={movie?.title}
          blurhash={posterImage?.hash || ""}
        />
      )}
      <div className="absolute inset-0 flex items-center justify-center">
        <Link
          to={`/movie/${movie.slug}`}
          className="mx-auto flex h-4/5 w-2/3 items-center justify-center"
        >
          <PlayIcon size={100} />
        </Link>
      </div>
    </>
  )
}

export default SpotlightItemMovie
