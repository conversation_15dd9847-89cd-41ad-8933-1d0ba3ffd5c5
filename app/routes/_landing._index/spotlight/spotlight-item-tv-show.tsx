import { BlurhashImage } from "@/components/common"
import { PlayIcon } from "@/components/icons"
import { type FragmentType, useFragment } from "@/gql"
import { type AppImage } from "@/gql/graphql"
import { TvShowFragment } from "@/lib/graphql/fragments"
import { useLanguage } from "@/lib/store"
import { baseUrl } from "@/lib/utils"
import { Link } from "react-router"

interface Props {
  data: FragmentType<typeof TvShowFragment>
  titleImage?: AppImage
  posterImage?: AppImage
}

const SpotlightItemTvShow = ({ data, titleImage, posterImage }: Props) => {
  const tvShow = useFragment(TvShowFragment, data)
  const { language } = useLanguage()

  return (
    <>
      <div className="absolute top-1/3 z-10 flex size-full flex-col gap-y-4 px-8 text-white md:w-1/2 lg:bottom-1/3 lg:w-1/3">
        <div className="flex h-full max-h-56 items-end justify-center">
          {titleImage && (
            <img
              className="object-contain"
              alt={tvShow?.title}
              src={`${baseUrl}/image/small/${titleImage.path}`}
            />
          )}
        </div>
        <p className="description-scrollbar hidden h-24 overflow-y-auto pr-2 lg:block">
          {language === "en" ? tvShow?.description_en : tvShow?.description_mz}
        </p>
        <div className="flex font-semibold">
          <span className="border-r-2 border-white pr-2">
            {tvShow?.production_year}
          </span>
          <span className="px-2">{tvShow?.age_label || ""}</span>
        </div>
        <div>
          {tvShow?.genre ? (
            <div>
              <span className="font-bold">Genre: </span>
              {tvShow.genre}
            </div>
          ) : null}
        </div>
      </div>
      {posterImage && (
        <BlurhashImage
          className="h-[75vh] w-full object-cover"
          src={`${baseUrl}/image/web-landscape/${posterImage.path}`}
          alt={tvShow?.title}
          blurhash={posterImage?.hash || ""}
        />
      )}
      <div className="absolute inset-0 flex items-center justify-center">
        <Link
          to={`/show/${tvShow.slug}`}
          className="mx-auto flex h-4/5 w-2/3 items-center justify-center"
        >
          <PlayIcon size={100} />
        </Link>
      </div>
    </>
  )
}

export default SpotlightItemTvShow
