import {
  Carousel,
  CarouselContent,
  CarouselDots,
  CarouselItem,
} from "@/components/ui/carousel"
import { type FragmentType, useFragment } from "@/gql"
import SpotlightItemMovie from "./spotlight-item-movie"
import SpotlightItemTvShow from "./spotlight-item-tv-show"
import { HOME_DATA_FRAGMENT } from "@/lib/graphql/fragments"
import SpotlightItemLiveShow from "./spotlight-item-live-show"
import { cn } from "@/lib/utils/cn"
import Autoplay from "embla-carousel-autoplay"

interface Props {
  spotlight: FragmentType<typeof HOME_DATA_FRAGMENT>
}

const Spotlight = ({ spotlight }: Props) => {
  const data = useFragment(HOME_DATA_FRAGMENT, spotlight)

  return (
    <section
      className={cn({
        "mt-[10vh]": data.homeDataList?.length === 0,
      })}
    >
      <Carousel
        opts={{
          loop: true,
        }}
        plugins={[
          Autoplay({
            delay: 5000,
          }),
        ]}
      >
        <CarouselContent>
          {data.homeDataList?.map((item) => {
            return (
              item && (
                <CarouselItem
                  className="relative h-[80vh] w-full 2xl:h-[75vh]"
                  key={item.id}
                >
                  {item?.homeable?.__typename === "Movie" && (
                    <SpotlightItemMovie
                      data={item.homeable}
                      titleImage={item?.titleImage || undefined}
                      posterImage={item?.posterLandscapeImage || undefined}
                    />
                  )}
                  {item?.homeable?.__typename === "TVShow" && (
                    <SpotlightItemTvShow
                      data={item.homeable}
                      titleImage={item?.titleImage || undefined}
                      posterImage={item?.posterLandscapeImage || undefined}
                    />
                  )}
                  {item?.homeable?.__typename === "Live" && (
                    <SpotlightItemLiveShow
                      data={item.homeable}
                      titleImage={item?.titleImage || undefined}
                      posterImage={item?.posterLandscapeImage || undefined}
                    />
                  )}
                  <div className="pointer-events-none absolute inset-0 bg-gradient-to-b from-black/30 via-black/0 to-black/0" />
                  <div className="pointer-events-none absolute inset-0 bg-gradient-to-b from-black/0 via-black/0 to-[#0a0a0a]" />
                </CarouselItem>
              )
            )
          })}
        </CarouselContent>
        {data?.homeDataList && data.homeDataList.length > 1 && (
          <div className="absolute bottom-0 flex w-full items-center justify-center gap-x-2 py-4">
            {/* <CarouselPrevious /> */}
            <CarouselDots />
            {/* <CarouselNext /> */}
          </div>
        )}
      </Carousel>
    </section>
  )
}

export default Spotlight
