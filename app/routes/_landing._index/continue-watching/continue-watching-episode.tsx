import { BlurhashImage } from "@/components/common"
import { PlayIcon } from "@/components/icons"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { buttonVariants } from "@/components/ui/button"
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card"
import { Progress } from "@/components/ui/progress"
import { type FragmentType, useFragment } from "@/gql"
import { EPISODE_FRAGMENT } from "@/lib/graphql/fragments"
import { useLanguage } from "@/lib/store"
import { baseUrl, formatDuration } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"
import { useState } from "react"
import { Link } from "react-router"
import { useDebounce } from "use-debounce"

interface Props {
  episode: FragmentType<typeof EPISODE_FRAGMENT>
}

const ContinueWatchingEpisode = ({ episode }: Props) => {
  const data = useFragment(EPISODE_FRAGMENT, episode)
  const [isOverButton, setIsOverButton] = useState(false)
  const [isOverPopover, setIsOverPopover] = useState(false)

  const { language } = useLanguage()

  const [isOverAny] = useDebounce(isOverButton || isOverPopover, 150)

  // const watchedDuration = data.duration
  const totalDuration = data?.continueWatching?.total_duration ?? 0
  const watchedDuration = data?.continueWatching?.watched_duration ?? 0
  const percentageWatched =
    totalDuration > 0
      ? Math.min(Math.round((watchedDuration / totalDuration) * 100), 100)
      : 0

  return (
    <HoverCard openDelay={100} closeDelay={100}>
      <HoverCardTrigger asChild>
        <Link
          to={`/show/${data.tv_show_id}/watch?eid=${data.id}`}
          className={cn(
            buttonVariants({ variant: "link" }),
            "relative size-full rounded-none p-0 transition duration-300 ease-in"
          )}
          onMouseEnter={() => {
            setIsOverButton(true)
          }}
          onMouseLeave={() => {
            setIsOverButton(false)
          }}
        >
          <AspectRatio className="overflow-hidden" ratio={16 / 9}>
            <div
              className={cn(
                "absolute inset-0 z-10 bg-transparent opacity-0 transition duration-300 ease-out",
                {
                  "bg-black/75 opacity-100": isOverAny,
                }
              )}
            ></div>
            <div className="absolute inset-0 z-[99] mx-auto flex flex-col items-center justify-center bg-black/20 text-lg">
              <PlayIcon size={56} />
              Resume
            </div>
            <BlurhashImage
              src={`${baseUrl}/image/medium/${data.imageLandscape?.path}`}
              alt={data.title}
              blurhash={data.imageLandscape?.hash || ""}
            />
            <Progress
              value={percentageWatched}
              className="absolute bottom-0 z-[100] w-full"
            />
          </AspectRatio>
        </Link>
      </HoverCardTrigger>
      <HoverCardContent
        className="w-[350px] overflow-hidden bg-popover/90 px-0"
        side="right"
        sideOffset={-200}
        onMouseLeave={() => {
          setIsOverPopover(false)
        }}
        onMouseEnter={() => {
          setIsOverPopover(true)
        }}
      >
        <div>
          <AspectRatio ratio={16 / 9}>
            <Link
              className="relative"
              to={`/show/${data.tv_show_id}/watch?eid=${data.id}`}
            >
              <img
                className="size-full object-contain"
                alt={data.title}
                src={`${baseUrl}/image/medium/${data.imageLandscape?.path}`}
              />
              <div className="absolute inset-0 flex size-full items-center justify-center bg-black/55 duration-300 hover:scale-105 hover:bg-black/75">
                <PlayIcon size={128} />
              </div>
            </Link>
          </AspectRatio>
          <div className="space-y-2 p-2">
            <div>{data.title}</div>
            <div>
              <div className="flex font-semibold">
                {data?.duration > 0 ? (
                  <span>{formatDuration(data.duration)}</span>
                ) : null}
              </div>
            </div>
            <div className="line-clamp-4 whitespace-pre-line">
              {language === "mz" ? data.description_mz : data.description_en}
            </div>
          </div>
        </div>
      </HoverCardContent>
    </HoverCard>
  )
}

export default ContinueWatchingEpisode
