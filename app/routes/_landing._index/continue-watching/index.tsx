import {
  Carousel,
  Carousel<PERSON>ontent,
  Carouse<PERSON><PERSON><PERSON>,
  CarouselN<PERSON><PERSON>,
  CarouselPrevious,
} from "@/components/ui/carousel"
import { type FragmentType, useFragment } from "@/gql"
import CONTINUE_WATCHING_FRAGMENT from "@/lib/graphql/fragments/continue-watching-fragment"
import ContinueWatchingMovie from "./continue-watching-movie"
import ContinueWatchingEpisode from "./continue-watching-episode"

interface Props {
  continueWatching: Array<FragmentType<typeof CONTINUE_WATCHING_FRAGMENT>>
}

const ContinueWatching = ({ continueWatching }: Props) => {
  const data = useFragment(CONTINUE_WATCHING_FRAGMENT, continueWatching)

  return (
    <>
      <section className="relative my-2 overflow-x-hidden">
        <h2 className="px-8 py-2 text-2xl font-bold">Continue Watching</h2>
        <Carousel>
          <CarouselContent>
            {data.map((item) => (
              <CarouselItem className="basis-1/4" key={item.id}>
                {item.watchable?.__typename === "Movie" && (
                  <div className="relative px-4">
                    <ContinueWatchingMovie movie={item.watchable} />
                  </div>
                )}
                {item.watchable?.__typename === "TVShowEpisodes" && (
                  <div className="relative px-4">
                    <ContinueWatchingEpisode episode={item.watchable} />
                  </div>
                )}
              </CarouselItem>
            ))}
          </CarouselContent>
          {data && data.length > 5 && (
            <>
              <div className="absolute left-0 top-0 flex h-full w-8 items-center justify-between">
                <CarouselPrevious className="h-full w-8 rounded-none border-0" />
              </div>
              <div className="absolute right-0 top-0 flex h-full w-8 items-center justify-between">
                <CarouselNext className="h-full w-8 rounded-none border-0 border-none" />
              </div>
            </>
          )}
        </Carousel>
      </section>
    </>
  )
}

export default ContinueWatching
