import { graphql } from "@/gql"
import { graphqlClient } from "@/lib/utils"
import Spotlight from "./spotlight"
import OtherCategories from "./other-categories"
import ContinueWatching from "./continue-watching"
import { useQuery } from "@tanstack/react-query"

const HomeData = graphql(`
  query HomeData {
    getHomeData(first: 20) {
      data {
        id
        category
        order
        ...HomeDataFragment
      }
    }
    getContinueWatchingList {
      ...ContinueWatchingFragment
    }
  }
`)

export const meta = () => {
  return [
    { title: "Enila" },
    {
      name: "description",
      content:
        "Enila is a user friendly streaming platform made with love in Aizawl, Mizoram",
    },
    {
      property: "og:title",
      content: `Enila`,
    },
    {
      property: "og:url",
      content: `https://enila.in`,
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      property: "og:image",
      itemProp: "image",
      content: "/enila_logo.png",
    },
    {
      property: "og:image:height",
      content: "600",
    },
    {
      property: "og:image:width",
      content: "600",
    },
    {
      property: "robots",
      content: "index,follow",
    },
    {
      property: "og:description",
      content:
        "Enila is a user friendly streaming platform made with love in Aizawl, Mizoram",
    },
    {
      property: "twitter:card",
      content: "summary_large_image",
    },
    {
      property: "twitter:url",
      content: `https://enila.in`,
    },
    {
      property: "twitter:title",
      content: "Enila",
    },
    {
      property: "twitter:description",
      content:
        "Enila is a user friendly streaming platform made with love in Aizawl, Mizoram",
    },
    {
      property: "twitter:image",
      content: "/enila_logo.png",
    },
    {
      property: "og:site_name",
      content: "Enila",
    },
  ]
}

export default function Index() {
  // const { data } = useQuery(["home-data"], async () =>
  //   graphqlClient.request(HomeData)
  // )

  const { data } = useQuery({
    queryKey: ["home-data"],
    queryFn: async () => graphqlClient.request(HomeData),
  })

  const spotlight = data?.getHomeData?.data?.find(
    (item) => item.category === "spotlight"
  )

  const otherCategories = data?.getHomeData?.data?.filter(
    (item) => item.category !== "spotlight"
  )

  const continueWatchingList = data?.getContinueWatchingList || []

  return (
    <>
      {spotlight && <Spotlight spotlight={spotlight} />}
      {continueWatchingList.length > 0 && (
        <ContinueWatching continueWatching={continueWatchingList} />
      )}
      {otherCategories && otherCategories.length > 0 && (
        <OtherCategories otherCategories={otherCategories} />
      )}
    </>
  )
}
