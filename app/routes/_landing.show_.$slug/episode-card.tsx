import { FavoriteIcon } from "@/components/icons"
import { Button } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"
import { type FragmentType, useFragment } from "@/gql"
import { WatchListableType } from "@/gql/graphql"
import { useWatchList } from "@/lib/hooks"
import { baseUrl, formatDuration } from "@/lib/utils"
import EpisodeAction from "./episode-action-ui"
import { useNavigate } from "react-router"
import { useLanguage, useRentEpisodeDialogStore } from "@/lib/store"
import { EPISODE_FRAGMENT } from "@/lib/graphql/fragments"
import { BlurhashImage } from "@/components/common"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { useQueryClient } from "@tanstack/react-query"

interface Props {
  data: FragmentType<typeof EPISODE_FRAGMENT>
  episodePrice: number
}

const EpisodeCard = ({ data, episodePrice }: Props) => {
  const episode = useFragment(EPISODE_FRAGMENT, data)
  const navigate = useNavigate()
  const queryClient = useQueryClient()

  const { addToWatchList, deleteFromWatchList } = useWatchList()
  const { openRentEpisodeDialog } = useRentEpisodeDialogStore()

  const { language } = useLanguage()

  const handleAddToWatchList = () => {
    addToWatchList.mutate(
      {
        id: episode.id,
        contentType: WatchListableType.TvShowEpisode,
      },
      {
        onSuccess: () => {
          void queryClient.refetchQueries()
          toast({
            description: `${episode.title} added to watch list`,
            variant: "default",
            className: "bg-white text-black",
          })
        },
      }
    )
  }

  const handleDeleteFromWatchList = () => {
    deleteFromWatchList.mutate(
      {
        id: episode.isAddedToWatchList.toString(),
      },
      {
        onSuccess: () => {
          void queryClient.refetchQueries()
          toast({
            description: `${episode.title} removed from watchlist`,
            variant: "default",
            className: "bg-white text-black",
          })
        },
      }
    )
  }

  const handleEpisodeClick = () => {
    if (episode.is_rented || episode.is_free) {
      void navigate({
        pathname: "watch",
        search: `?eid=${episode.id}`,
      })
    } else if (episode.for_subscriber) {
      void navigate("/subscription")
    } else {
      openRentEpisodeDialog(episode.id, episodePrice)
    }
  }

  return (
    <>
      <div className="flex h-full items-center space-x-2" key={episode.id}>
        <Button
          onClick={handleEpisodeClick}
          variant="ghost"
          className="size-full p-1 text-start"
        >
          <div className="grid w-full grid-cols-12">
            <div className="col-span-3">
              <AspectRatio className="overflow-hidden" ratio={16 / 9}>
                <BlurhashImage
                  className="size-full"
                  alt={episode.title}
                  src={`${baseUrl}/image/medium/${episode.imageLandscape?.path}`}
                  blurhash={episode.imageLandscape?.hash || ""}
                />
              </AspectRatio>
            </div>
            <div className="col-span-6 flex flex-col px-4 lg:col-span-7">
              <div className="grow">
                <p>{episode?.title}</p>
                <p className="line-clamp-1 whitespace-pre-wrap text-sm md:line-clamp-3">
                  {language === "en"
                    ? episode?.description_en
                    : episode?.description_mz}
                </p>
              </div>
              <div className="flex w-full">
                <span className="whitespace-pre-line border-r-2 border-white pr-2">
                  {episode?.sub_header}
                </span>
                <span className="px-2">
                  {formatDuration(episode?.duration || 0)}
                </span>
              </div>
            </div>
            <div className="col-span-3 my-auto lg:col-span-2">
              <EpisodeAction episodePrice={episodePrice} episode={episode} />
            </div>
          </div>
        </Button>
        {episode.isAddedToWatchList ? (
          <Button onClick={handleDeleteFromWatchList} variant="ghost">
            <FavoriteIcon fill={true} color="theme-yellow" />
          </Button>
        ) : (
          <Button onClick={handleAddToWatchList} variant="ghost">
            <FavoriteIcon />
          </Button>
        )}
      </div>
    </>
  )
}

export default EpisodeCard
