import { Rupee } from "@/components/common"
import { But<PERSON>, buttonVariants } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTrigger,
} from "@/components/ui/dialog"
import { SEASON_BY_ID_QUERY } from "@/lib/graphql/queries"
import { graphqlClient } from "@/lib/utils"
import { cn } from "@/lib/utils/cn"
import { useState } from "react"
import RentSeasonEpisodeList from "./rent-season-episode-list"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useNavigate } from "react-router"
import { useRazorpay, useUser } from "@/lib/hooks"
import { RENT_MULTIPLE_EPISODES } from "@/lib/graphql/mutation"
import { useMutation, useQuery } from "@tanstack/react-query"

interface Props {
  episodePrice: number
  seasonId: string
}

const RentSeason = ({ episodePrice, seasonId }: Props) => {
  const [open, setOpen] = useState(false)
  const [totalAmount, setTotalAmount] = useState(0)
  const { data: user } = useUser()
  const navigate = useNavigate()

  const { handleRazorpay } = useRazorpay()

  const handleDialog = (open: boolean) => {
    setOpen(open)
    setTotalAmount(0)
    setSelectedIds([])
  }

  const [selectedIds, setSelectedIds] = useState<string[]>([])

  const rentMultipleEpisodes = useMutation({
    mutationFn: async () => {
      return await graphqlClient.request(RENT_MULTIPLE_EPISODES, {
        ids: selectedIds,
        totalAmount: selectedIds.length * episodePrice,
      })
    },
    onSuccess: (data) => {
      if (data?.rentMultipleEpisodes?.__typename === "PhonePePaymentResponse") {
        void navigate(data.rentMultipleEpisodes.goto_url)
      }
      if (
        data?.rentMultipleEpisodes?.__typename === "RazorPayPaymentResponse"
      ) {
        handleRazorpay({
          price: totalAmount,
          orderId: data?.rentMultipleEpisodes.order_id,
          user: user?.getMe,
          onLoadCallback: () => handleDialog(false),
          redirectUrl: window.location.href,
        })
      }
    },
  })

  const { data } = useQuery({
    queryKey: ["season-by-id", seasonId],
    queryFn: async () =>
      graphqlClient.request(SEASON_BY_ID_QUERY, {
        id: seasonId,
      }),
    enabled: !!seasonId,
  })

  const handleSelect = (id: string) => {
    let newSelectedIds

    if (selectedIds.includes(id)) {
      newSelectedIds = selectedIds.filter((i) => i !== id)
    } else {
      newSelectedIds = [...selectedIds, id]
    }

    setSelectedIds(newSelectedIds)
    setTotalAmount(newSelectedIds.length * episodePrice)
  }

  const handleMultipleRent = () => {
    rentMultipleEpisodes.mutate()
  }

  return (
    <Dialog open={open} onOpenChange={handleDialog}>
      <DialogTrigger
        onClick={() => handleDialog(true)}
        className={cn(buttonVariants({ variant: "default" }), "w-full")}
      >
        Rent
      </DialogTrigger>
      <DialogContent showCloseButton={false} className="max-w-2xl">
        <DialogHeader className="flex w-full flex-row items-center justify-between">
          <span>{data?.tvShowSeasonById?.title}</span>
          <Button
            onClick={handleMultipleRent}
            isLoading={rentMultipleEpisodes.isPending}
            disabled={selectedIds.length === 0}
            className="px-4"
          >
            Rent Multiple Episodes{" "}
            {totalAmount > 0 ? <Rupee amount={totalAmount} /> : null}
          </Button>
        </DialogHeader>
        <ScrollArea className="h-96">
          <div>
            {data?.tvShowSeasonById?.episodes?.map(
              (episode, index) =>
                episode && (
                  <RentSeasonEpisodeList
                    key={index}
                    data={episode}
                    episodePrice={episodePrice}
                    closeParentDialog={() => handleDialog(false)}
                    handleSelect={handleSelect}
                  />
                )
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}

export default RentSeason
