import { Button, buttonVariants } from "@/components/ui/button"
import { type FragmentType, graphql, useFragment } from "@/gql"
import { useNavigate } from "react-router"
import RentSeason from "./rent-season"
import { cn } from "@/lib/utils/cn"

const SHOW_ACTION_FRAGMENT = graphql(`
  fragment ShowAction on TVShowSeason {
    id
    rentable_status
    is_rented
    title
    # price
    episodes {
      id
      ...TVEpisodeFragment
    }
  }
`)

interface Props {
  data: FragmentType<typeof SHOW_ACTION_FRAGMENT>
  episodePrice: number
  selectedSeasonId: string
}

const ShowAction = ({ data, episodePrice, selectedSeasonId }: Props) => {
  const season = useFragment(SHOW_ACTION_FRAGMENT, data)

  const navigate = useNavigate()
  const handlePlay = () => {
    const episodeId = season?.episodes?.[0]?.id || ""
    if (episodeId) {
      navigate({
        pathname: "watch",
        search: `?eid=${episodeId}`,
      })
    }
  }

  if (season.rentable_status === "for_subscriber") {
    return (
      <a
        href={`/subscription?ref=${window.location.pathname}${window.location.search}`}
        className={cn(buttonVariants({ variant: "default" }), "w-full")}
      >
        Subscribe to Play
      </a>
    )
  } else if (season.is_rented || season.rentable_status === "free") {
    return (
      <Button onClick={handlePlay} className="w-full">
        Play
      </Button>
    )
  } else if (season.rentable_status === "rent") {
    return (
      <RentSeason seasonId={selectedSeasonId} episodePrice={episodePrice} />
    )
  } else {
    return null
  }
}

export default ShowAction
