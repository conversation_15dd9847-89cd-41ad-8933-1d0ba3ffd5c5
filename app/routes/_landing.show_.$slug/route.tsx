import { SEASON_BY_ID_QUERY } from "@/lib/graphql/queries"
import { baseUrl, graphqlClient } from "@/lib/utils"
import { useLoaderData, useSearchParams } from "react-router"
import { useEffect, useState } from "react"
import { cn } from "@/lib/utils/cn"
import { Button } from "@/components/ui/button"
import EpisodeCard from "./episode-card"
import ShowAction from "./show-action"
import { useWatchList } from "@/lib/hooks"
import { WatchListableType } from "@/gql/graphql"
import { toast } from "@/components/ui/use-toast"
import { FavoriteIcon } from "@/components/icons"
import { useLanguage } from "@/lib/store"
import {
  BlurhashImage,
  CastModal,
  MuxPlayerTrailer,
  PageError,
  PageLoader,
  RentEpisodeDialog,
} from "@/components/common"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import type { Route } from "./+types/route"
import SHOW_BY_SLUG_QUERY from "@/lib/graphql/queries/show-by-slug-query"

export async function loader({ params }: Route.LoaderArgs) {
  const response = await graphqlClient.request(SHOW_BY_SLUG_QUERY, {
    slug: params.slug,
  })

  const show = response.getTvShowBySlug
  if (show) {
    return { show }
  } else {
    throw new Error("404. Not Found")
  }
}

export const meta = ({ data, location }: Route.MetaArgs) => {
  return [
    { title: `Enila - ${data?.show?.title}` },
    { name: "description", content: `${data?.show?.description_en}` },
    {
      property: "og:title",
      content: `Enila - ${data?.show?.title}`,
    },
    {
      property: "og:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      property: "og:image",
      itemProp: "image",
      content: data?.show?.imageLandscape?.path
        ? `${baseUrl}/image/small/${data?.show?.imageLandscape?.path}`
        : "",
    },
    {
      property: "og:image:height",
      content: "600",
    },
    {
      property: "og:image:width",
      content: "600",
    },
    {
      property: "robots",
      content: "index,follow",
    },
    {
      property: "og:description",
      content: `${data?.show?.description_en}`,
    },
    {
      property: "twitter:card",
      content: "summary_large_image",
    },
    {
      property: "twitter:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "twitter:title",
      content: `Enila - ${data?.show?.title}`,
    },
    {
      property: "twitter:description",
      content: `${data?.show?.description_en}`,
    },
    {
      property: "twitter:image",
      content: data?.show?.imageLandscape?.path
        ? `${baseUrl}/image/small/${data?.show?.imageLandscape?.path}`
        : "",
    },
    {
      property: "og:site_name",
      content: "Enila",
    },
  ]
}

const ShowBySlug = () => {
  const { show } = useLoaderData<typeof loader>()
  const queryClient = useQueryClient()
  const [searchParams, setSearchParams] = useSearchParams()

  const { language } = useLanguage()

  const [selectedSeasonId, setSelectedSeasonId] = useState(searchParams.get("sid") || "")

  const {
    data: seasonById,
    isLoading: isLoadingSeasonById,
    isError: isErrorSeasonById,
  } = useQuery({
    queryKey: ["season-by-slug", selectedSeasonId, show?.id],
    queryFn: async () =>
      graphqlClient.request(SEASON_BY_ID_QUERY, {
        id: selectedSeasonId,
      }),
    enabled: !!selectedSeasonId,
  })

  const { addToWatchList, deleteFromWatchList } = useWatchList()

  const handleSelectSeason = (id: string) => {
    setSelectedSeasonId(id)
    setSearchParams(
      (params) => {
        params.set("sid", id)
        return params
      },
      {
        preventScrollReset: true,
      }
    )
  }

  const handleAddToWatchList = () => {
    const id = seasonById?.tvShowSeasonById?.id
    const showTitle = show?.title
    const seasonTitle = seasonById?.tvShowSeasonById?.title
    if (id) {
      addToWatchList.mutate(
        {
          id: id,
          contentType: WatchListableType.TvShowSeason,
        },
        {
          onSuccess: () => {
            void queryClient.refetchQueries()
            toast({
              description: `${showTitle} ${seasonTitle} added to watch list`,
              variant: "default",
            })
          },
        }
      )
    }
  }

  const handleDeleteFromWatchList = () => {
    const id = seasonById?.tvShowSeasonById?.isAddedToWatchList
    const showTitle = show?.title
    const seasonTitle = seasonById?.tvShowSeasonById?.title
    if (id) {
      deleteFromWatchList.mutate(
        {
          id: id.toString(),
        },
        {
          onSuccess: () => {
            void queryClient.refetchQueries()
            toast({
              description: `${showTitle} ${seasonTitle} removed from watchlist`,
              variant: "default",
            })
          },
        }
      )
    }
  }

  useEffect(() => {
    if (show?.seasons && show?.seasons.length > 0 && !selectedSeasonId) {
      const firstSeason = show.seasons[0]
      if (firstSeason) {
        setSelectedSeasonId(firstSeason.id)
        setSearchParams(
          (params) => {
            params.set("sid", firstSeason.id)
            return params
          },
          {
            preventScrollReset: true,
          }
        )
      }
    }
  }, [show, searchParams, selectedSeasonId, setSearchParams])

  if (isLoadingSeasonById) {
    return <PageLoader />
  }

  if (isErrorSeasonById) {
    return <PageError />
  }

  const seasons = show?.seasons

  return (
    <>
      <div className="relative h-[90vh] overflow-hidden">
        {seasonById?.tvShowSeasonById?.trailer?.cdn_playback_id ? (
          <>
            <MuxPlayerTrailer
              playbackId={seasonById.tvShowSeasonById.trailer.cdn_playback_id}
              posterUrl={show?.imageLandscape?.path || ""}
            />
          </>
        ) : (
          <>
            <BlurhashImage
              className="size-full object-cover"
              alt={show?.title || ""}
              src={`${baseUrl}/image/medium/${show?.imageLandscape?.path}`}
              blurhash={show?.imageLandscape?.hash || ""}
            />
          </>
        )}
        <div className="pointer-events-none absolute inset-0 bg-gradient-to-r from-black/25 to-black/0" />
        <div className="pointer-events-none absolute inset-0 bg-gradient-to-b from-black/25 to-black/0" />
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-black/25" />

        <div className="absolute top-[40%] z-40 w-1/2 space-y-2 px-8 xl:w-2/5 2xl:w-1/3">
          <h1 className="text-4xl font-bold">{show?.title}</h1>
          <p className="description-scrollbar h-auto max-h-32 overflow-y-auto pr-2">
            {language === "en" ? show?.description_en : show?.description_mz}
          </p>
          <p className="description-scrollbar line-clamp-2 h-auto max-h-16 overflow-y-clip pr-2">
            {show?.casts}
          </p>
          <div className="flex w-full justify-end pr-2">
            <CastModal cast={show?.casts || ""} />
          </div>
          {show?.genre ? (
            <div>
              <span className="font-bold">Genre: </span>
              {show.genre}
            </div>
          ) : null}
          <div className="flex font-semibold">
            <span className="border-r-2 border-white pr-2">{show?.production_year}</span>
            <span className="px-2">{show?.age_label}</span>
          </div>

          {seasonById?.tvShowSeasonById && (
            <ShowAction
              episodePrice={show?.price_per_episode || 0}
              data={seasonById.tvShowSeasonById}
              selectedSeasonId={selectedSeasonId}
            />
          )}

          {seasonById?.tvShowSeasonById?.isAddedToWatchList ? (
            <Button className="space-x-2" onClick={handleDeleteFromWatchList} variant="ghost">
              <span>Remove from watchlist</span>
              <FavoriteIcon fill={true} color="theme-yellow" />
            </Button>
          ) : (
            <Button className="space-x-2" onClick={handleAddToWatchList} variant="ghost">
              <span>Add to watchlist </span>
              <FavoriteIcon />
            </Button>
          )}
        </div>
      </div>
      <div className="my-4 flex flex-wrap gap-x-8 px-2 lg:px-8">
        {seasons &&
          seasons.map((season) => {
            return (
              season && (
                <div
                  key={season.id}
                  className={cn(season.id === selectedSeasonId ? "border-b-2 border-white" : "")}
                >
                  <Button
                    onClick={(e) => {
                      e.preventDefault()
                      handleSelectSeason(season.id)
                    }}
                    variant="link"
                    className="hover:no-underline"
                  >
                    {season.title}
                  </Button>
                </div>
              )
            )
          })}
      </div>
      <div className="flex w-full flex-col gap-y-8 px-2 pb-8 lg:w-1/2 lg:px-8">
        {seasonById?.tvShowSeasonById?.episodes?.map((episode, index) => {
          return (
            episode && (
              <EpisodeCard key={index} data={episode} episodePrice={show?.price_per_episode || 0} />
            )
          )
        })}
      </div>
      <RentEpisodeDialog />
    </>
  )
}

export default ShowBySlug
