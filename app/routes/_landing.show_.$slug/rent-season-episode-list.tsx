import { Button } from "@/components/ui/button"
import { type FragmentType, useFragment } from "@/gql"
import { baseUrl, formatDuration } from "@/lib/utils"
import { useNavigate } from "react-router"
import { useLanguage, useRentEpisodeDialogStore } from "@/lib/store"
import { EPISODE_FRAGMENT } from "@/lib/graphql/fragments"
import { type TvEpisodeFragmentFragment } from "@/gql/graphql"
import { Rupee } from "@/components/common"

interface Props {
  data: FragmentType<typeof EPISODE_FRAGMENT>
  episodePrice: number
  closeParentDialog?: () => void
  handleSelect: (id: string) => void
}

const RentSeasonEpisodeList = ({
  data,
  episodePrice,
  closeParentDialog,
  handleSelect,
}: Props) => {
  const episode = useFragment(EPISODE_FRAGMENT, data)
  const navigate = useNavigate()
  const { openRentEpisodeDialog } = useRentEpisodeDialogStore()

  const { language } = useLanguage()

  const handleOpenChange = () => {
    closeParentDialog && closeParentDialog()
    openRentEpisodeDialog(episode.id, episodePrice)
  }

  const handleEpisodeClick = () => {
    if (episode.is_rented || episode.is_rented) {
      navigate({
        pathname: "watch",
        search: `?eid=${episode.id}`,
      })
    } else if (episode.for_subscriber) {
      navigate("/subscription")
    } else {
      handleOpenChange()
    }
  }

  return (
    <>
      <div className="flex h-full items-center space-x-2" key={episode.id}>
        <div className="my-2 size-full p-1 text-start">
          <div className="grid grid-cols-12">
            <input
              onClick={() => handleSelect(episode.id)}
              type="checkbox"
              className="col-span-1 my-auto size-4"
            />
            <img
              className="col-span-3 size-full"
              alt={episode.title}
              src={`${baseUrl}/image/medium/${episode.imageLandscape?.path}`}
            />
            {/* <BlurhashImage */}
            {/*   className="col-span-3 size-full" */}
            {/*   alt={episode.title} */}
            {/*   src={`${baseUrl}/image/medium/${episode.imageLandscape?.path}`} */}
            {/*   blurhash={episode.imageLandscape?.hash || ""} */}
            {/* /> */}
            <div className="col-span-6 flex flex-col px-4 lg:col-span-6">
              <div className="grow">
                <p>{episode?.title}</p>
                <p className="line-clamp-1 whitespace-pre-wrap text-sm md:line-clamp-2">
                  {language === "en"
                    ? episode?.description_en
                    : episode?.description_mz}
                </p>
              </div>
              <div className="flex">
                <span className="border-r-2 border-white pr-2">
                  {episode?.sub_header}
                </span>
                <span className="px-2">
                  {formatDuration(episode?.duration || 0)}
                </span>
              </div>
            </div>
            <div className="col-span-3 my-auto lg:col-span-2">
              <EpisodeAction
                handleEpisodeClick={handleEpisodeClick}
                episode={episode}
                episodePrice={episodePrice}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

interface EpisodeActionProps {
  episode: TvEpisodeFragmentFragment
  episodePrice: number
  handleEpisodeClick: () => void
}

const EpisodeAction = ({
  episode,
  episodePrice,
  handleEpisodeClick,
}: EpisodeActionProps) => {
  if (episode.is_rented || episode.is_free) {
    return (
      <Button onClick={handleEpisodeClick} className="w-full">
        Play
      </Button>
    )
  } else if (episode.for_subscriber) {
    return (
      <Button onClick={handleEpisodeClick} className="w-full">
        Subscribe
      </Button>
    )
  } else {
    return (
      <Button onClick={handleEpisodeClick} className="w-full">
        Rent <Rupee amount={episodePrice} />
      </Button>
    )
  }
}

export default RentSeasonEpisodeList
