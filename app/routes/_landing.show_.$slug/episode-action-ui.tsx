import { Rupee } from "@/components/common"
import { buttonVariants } from "@/components/ui/button"
import { type TvEpisodeFragmentFragment } from "@/gql/graphql"

interface Props {
  episode: TvEpisodeFragmentFragment
  episodePrice: number
}

// This is more for visual, the real handling will be done in episode-card
const EpisodeAction = ({ episode, episodePrice }: Props) => {
  if (episode.is_rented || episode.is_free) {
    return (
      <div
        className={buttonVariants({
          variant: "default",
          className: "w-full",
        })}
      >
        {episode.continueWatching?.watched_duration ? "Resume" : "Play"}
      </div>
    )
  } else if (episode.for_subscriber) {
    return (
      <div
        className={buttonVariants({ variant: "default", className: "w-full" })}
      >
        Subscribe
      </div>
    )
  } else {
    return (
      <div
        className={buttonVariants({
          variant: "default",
          className: "w-full",
        })}
      >
        Rent <Rupee amount={episodePrice} />
      </div>
    )
  }
}

export default EpisodeAction
