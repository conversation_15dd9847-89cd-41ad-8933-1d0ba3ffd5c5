export const meta= () => {
  return [{ title: "Enila - Terms and Conditions" }]
}

const TermsAndConditions = () => {
  return (
    <div className="mx-auto flex h-full min-h-screen max-w-4xl flex-col px-4 pt-[10vh] lg:px-0">
      <h1 className="text-4xl font-bold">Terms and Conditions</h1>
      <p className="mt-4">
        Welcome to Enila! We’re delighted to have you here. Before you dive into
        enjoying our vast selection of digital content, we’d like to lay out
        some ground rules in a clear and straightforward manner.
      </p>

      <h2 className="mt-4 font-bold">1. Agreement Acceptance:</h2>
      <p>
        When you create an account, watch videos, make a purchase, download our
        apps, or simply visit or use Enila, you’re agreeing to this Agreement
        and consenting to engage with us electronically. If you’re representing
        an entity other than yourself, please ensure you have the authority to
        bind that entity to these terms. By continuing to use Enila after any
        revisions to this Agreement, you accept those changes.
      </p>

      <h2 className="mt-4 font-bold">2. Account Guidelines:</h2>
      <p>
        To access certain features, such as viewing content, you’ll need to
        create an account. However, if you’re under 13, please refrain from
        creating an account unless you have parental consent. If you’re a parent
        granting access to your child, please supervise their usage responsibly.
        Remember, you’re responsible for maintaining the security of your
        account, including keeping your login credentials confidential. Sharing
        your account login isn’t permitted and may result in the suspension of
        your account.
      </p>

      <h2 className="mt-4 font-bold">3. Privacy Matters:</h2>
      <p>
        Your privacy is important to us. Take a moment to review our Privacy
        Policy to understand what information we collect, how we use it, and who
        we share it with. By creating an account, you agree to allow Enila
        access to certain account information and activities. For cancellation
        and refunds, please refer to our refund policy.
      </p>

      <h2 className="mt-4 font-bold">4. Payment and Subscription:</h2>
      <p>
        At Enila, we offer various purchase options for accessing content.
        Whether it’s rent, pay-per-view or subscription, you’ll need to provide
        a valid payment method for any purchases. Please note that all purchases
        are final and non-refundable.
      </p>

      <h2 className="mt-4 font-bold">5. Licensing and Content Usage:</h2>
      <p>
        Once you’ve purchased a content, you have the right to stream it for
        personal entertainment purposes only. However, there are restrictions,
        such as reselling or using programs for commercial purposes,
        redistributing, or making derivative works from them. Unauthorized
        reproduction or redistribution of content hosted on Enila is strictly
        prohibited and may result in legal consequences.
      </p>

      <h2 className="mt-4 font-bold">6. Limitation and Liability:</h2>
      <p>
        To the extent permitted by law, Enila, its parent company, directors,
        officers, employees, and partners shall not be liable for any damages,
        including loss of profits or data, arising from your use of Enila.
      </p>

      <h2 className="mt-4 font-bold">7. Disclaimer:</h2>
      <p>
        {`The Enila OTT Streaming Service is provided on an "as is" and "as
        available" basis, without warranties of any kind, either express or
        implied. Enila does not warrant that the service will be uninterrupted
        or error-free, that defects will be corrected, or that the service is
        free of viruses or other harmful components. You acknowledge and agree
        that your use of the service is at your own risk.
          `}
      </p>

      <h2 className="mt-4 font-bold">8. Contact Us:</h2>
      <p className="pb-8">
        If you have any questions or concerns about these Terms and Conditions
        or the Privacy Policy, please contact us at [Contact Information].
      </p>
    </div>
  )
}

export default TermsAndConditions
