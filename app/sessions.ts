import { createCookieSessionStorage } from 'react-router'

interface SessionData {
  token: string
}

interface SessionFlashData {
  error: string
  successMessage: string
}

const { commitSession, destroySession, getSession }
  = createCookieSessionStorage<SessionData, SessionFlashData>({
    cookie: {
      httpOnly: true,
      maxAge: 82_800_000, // 23 hours
      name: '__enila_session',
      path: '/',
      secrets: ['3n1las3kret'],
      secure: process.env.NODE_ENV === 'production',
    },
  })

export { commitSession, destroySession, getSession }
