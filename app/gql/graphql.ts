/* eslint-disable */
import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`. */
  DateTime: { input: any; output: any; }
  DateTimeUtc: { input: any; output: any; }
  /** Can be used as an argument to upload files using https://github.com/jaydenseric/graphql-multipart-request-spec */
  Upload: { input: any; output: any; }
};

/** Admin user */
export type Admin = {
  __typename?: 'Admin';
  id: Scalars['ID']['output'];
  username: Scalars['String']['output'];
};

export type AppImage = {
  __typename?: 'AppImage';
  hash?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  path?: Maybe<Scalars['String']['output']>;
};

export type AppPaginatorInfo = {
  __typename?: 'AppPaginatorInfo';
  currentPage: Scalars['Int']['output'];
  hasMorePages: Scalars['Boolean']['output'];
  total: Scalars['Int']['output'];
};

export type AppPaginatorInfo2 = {
  __typename?: 'AppPaginatorInfo2';
  currentPage: Scalars['Int']['output'];
  hasMorePages: Scalars['Boolean']['output'];
  lastPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type ApplePaymentOrder = {
  __typename?: 'ApplePaymentOrder';
  id: Scalars['ID']['output'];
  original_transaction_id?: Maybe<Scalars['String']['output']>;
  refund_amount?: Maybe<Scalars['Float']['output']>;
  refund_error?: Maybe<Scalars['String']['output']>;
  refund_status?: Maybe<Scalars['String']['output']>;
  transaction_id: Scalars['String']['output'];
};

export type AuthResponse = {
  __typename?: 'AuthResponse';
  admin?: Maybe<Admin>;
  token?: Maybe<Scalars['String']['output']>;
};

export type BasicStats = {
  __typename?: 'BasicStats';
  producer_share: Scalars['Float']['output'];
  studio_share: Scalars['Float']['output'];
  total_amount: Scalars['Float']['output'];
  total_user?: Maybe<Scalars['Int']['output']>;
};

export type ContentReportResponse = {
  __typename?: 'ContentReportResponse';
  producer_share: Scalars['Float']['output'];
  stats?: Maybe<Array<StatsData>>;
};

export type ContentTrailer = {
  __typename?: 'ContentTrailer';
  cdn_asset_id?: Maybe<Scalars['String']['output']>;
  cdn_content_status: Scalars['String']['output'];
  cdn_id: Scalars['String']['output'];
  cdn_playback_id?: Maybe<Scalars['String']['output']>;
  cdn_upload_url: Scalars['String']['output'];
  id: Scalars['ID']['output'];
};

export enum ContentType {
  Live = 'LIVE',
  Movie = 'MOVIE',
  TvShow = 'TV_SHOW'
}

export type ContinueWatchable = Movie | TvShowEpisodes;

export type CreateContentResponse = {
  __typename?: 'CreateContentResponse';
  content_id: Scalars['Int']['output'];
  upload_url: Scalars['String']['output'];
};

export enum DisplayType {
  Landscape = 'LANDSCAPE',
  Portrait = 'PORTRAIT',
  Portrait2x = 'PORTRAIT2x',
  Square = 'SQUARE',
  Square2x = 'SQUARE2x'
}

export type GenerateUrlResponse = {
  __typename?: 'GenerateUrlResponse';
  story_board_url?: Maybe<Scalars['String']['output']>;
  url: Scalars['String']['output'];
};

export type GetHomeDataPaginator = {
  __typename?: 'GetHomeDataPaginator';
  data?: Maybe<Array<HomeData>>;
  paginatorInfo?: Maybe<AppPaginatorInfo>;
};

export type GetLivesPaginator = {
  __typename?: 'GetLivesPaginator';
  data?: Maybe<Array<Live>>;
  paginatorInfo: AppPaginatorInfo;
};

export type GetMoviesPaginator = {
  __typename?: 'GetMoviesPaginator';
  data?: Maybe<Array<Movie>>;
  paginatorInfo: AppPaginatorInfo;
};

export type GetTvShowsPaginator = {
  __typename?: 'GetTvShowsPaginator';
  data?: Maybe<Array<TvShow>>;
  paginatorInfo: TvShowAppPaginatorInfo;
};

export type HomeData = {
  __typename?: 'HomeData';
  category: Scalars['String']['output'];
  display_type?: Maybe<Scalars['String']['output']>;
  homeDataList?: Maybe<Array<Maybe<HomeDataList>>>;
  id: Scalars['ID']['output'];
  order: Scalars['Int']['output'];
};

export enum HomeDataCategory {
  Genre = 'GENRE',
  Spotlight = 'SPOTLIGHT'
}

export type HomeDataList = {
  __typename?: 'HomeDataList';
  homeData?: Maybe<HomeData>;
  homeable?: Maybe<Homeable>;
  homeable_id: Scalars['ID']['output'];
  homeable_type: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  posterImage?: Maybe<AppImage>;
  posterLandscapeImage?: Maybe<AppImage>;
  titleImage?: Maybe<AppImage>;
};

/** A paginated list of HomeDataList items. */
export type HomeDataListPaginator = {
  __typename?: 'HomeDataListPaginator';
  /** A list of HomeDataList items. */
  data: Array<HomeDataList>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type Homeable = Live | Movie | TvShow;

export enum IosPurchaseType {
  Content = 'CONTENT',
  Subscription = 'SUBSCRIPTION'
}

export type IsAddedToWatchListResponse = {
  __typename?: 'IsAddedToWatchListResponse';
  watchlist_id?: Maybe<Scalars['Int']['output']>;
};

export type Live = {
  __typename?: 'Live';
  cdn_content_status?: Maybe<Scalars['String']['output']>;
  cdn_id?: Maybe<Scalars['String']['output']>;
  cdn_playback_id?: Maybe<Scalars['String']['output']>;
  cdn_stream_key?: Maybe<Scalars['String']['output']>;
  description_en?: Maybe<Scalars['String']['output']>;
  description_mz?: Maybe<Scalars['String']['output']>;
  for_subscriber: Scalars['Boolean']['output'];
  /** apple in app purchase id */
  iap_product_id?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  imageLandscape?: Maybe<AppImage>;
  imagePortrait?: Maybe<AppImage>;
  is_free: Scalars['Boolean']['output'];
  is_published?: Maybe<Scalars['Boolean']['output']>;
  is_rented?: Maybe<Scalars['Boolean']['output']>;
  liveStreamAssets?: Maybe<Array<Maybe<LiveStreamAsset>>>;
  price: Scalars['Float']['output'];
  search_tags?: Maybe<Array<Scalars['String']['output']>>;
  slug: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

/** A paginated list of Live items. */
export type LivePaginator = {
  __typename?: 'LivePaginator';
  /** A list of Live items. */
  data: Array<Live>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

/** This data is automatically populated by CDN webhook when live stream has technically ended. There could be scenarios where one live event creates multiple assets (When live is stopped and streamed again) */
export type LiveStreamAsset = {
  __typename?: 'LiveStreamAsset';
  id: Scalars['ID']['output'];
  live_id: Scalars['Int']['output'];
  master_asset_id?: Maybe<Scalars['String']['output']>;
  master_status: Scalars['String']['output'];
  master_url?: Maybe<Scalars['String']['output']>;
};

export type LiveStreamHealthResponse = {
  __typename?: 'LiveStreamHealthResponse';
  status: Scalars['String']['output'];
  stream_drift_deviation_from_rolling_avg: Scalars['Int']['output'];
  stream_drift_session_avg: Scalars['Int']['output'];
};

export type LoginOtpResponse = {
  __typename?: 'LoginOTPResponse';
  otp_id?: Maybe<Scalars['Int']['output']>;
};

/** Movie is one of the content type for this application */
export type Movie = {
  __typename?: 'Movie';
  age_label?: Maybe<Scalars['String']['output']>;
  casts?: Maybe<Scalars['String']['output']>;
  cdn_content_status: Scalars['String']['output'];
  cdn_id?: Maybe<Scalars['String']['output']>;
  cdn_playback_id?: Maybe<Scalars['String']['output']>;
  cdn_trailer_upload_url?: Maybe<Scalars['String']['output']>;
  cdn_upload_url?: Maybe<Scalars['String']['output']>;
  /** Note: Use sparringly */
  continueWatching?: Maybe<WatchHistory>;
  description_en?: Maybe<Scalars['String']['output']>;
  description_mz?: Maybe<Scalars['String']['output']>;
  duration: Scalars['Int']['output'];
  for_subscriber?: Maybe<Scalars['Boolean']['output']>;
  genre?: Maybe<Scalars['String']['output']>;
  /** apple in app purchase id */
  iap_product_id?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  /** Landscape image poster */
  imageLandscape?: Maybe<AppImage>;
  /** Portrait image poster */
  imagePortrait?: Maybe<AppImage>;
  imagePortrait2x?: Maybe<AppImage>;
  imageSquare?: Maybe<AppImage>;
  imageSquare2x?: Maybe<AppImage>;
  /** Check if auth user has added this content to watch list. If unauthenticated, this will return false. Note: Use Sparringly. Do not use this property when fetching list of episodes */
  isAddedToWatchList: Scalars['Int']['output'];
  is_free: Scalars['Boolean']['output'];
  is_published?: Maybe<Scalars['Boolean']['output']>;
  /** Note: Do not use this property when fetch list to preserve server resource. Use only for fetching detail content. Explicity nullable. If true, user has rented. if false or null, user has not rented the content */
  is_rented?: Maybe<Scalars['Boolean']['output']>;
  price: Scalars['Float']['output'];
  /** Amount (in percentage) */
  producer_share: Scalars['Float']['output'];
  production_year?: Maybe<Scalars['String']['output']>;
  search_tags?: Maybe<Array<Scalars['String']['output']>>;
  slug: Scalars['String']['output'];
  stats_slug: Scalars['String']['output'];
  subtitle_path_en?: Maybe<Scalars['String']['output']>;
  subtitle_path_mz?: Maybe<Scalars['String']['output']>;
  title: Scalars['String']['output'];
  trailer?: Maybe<ContentTrailer>;
  ttl?: Maybe<Scalars['Int']['output']>;
};

/** A paginated list of Movie items. */
export type MoviePaginator = {
  __typename?: 'MoviePaginator';
  /** A list of Movie items. */
  data: Array<Movie>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type Mutation = {
  __typename?: 'Mutation';
  /** Add Home data for home screen. Home data will contain list(horizontal) of MOVIES and/or tv show */
  addHomeData?: Maybe<HomeData>;
  /** Add home data items (for horizontal lists) */
  addHomeDataList?: Maybe<HomeDataList>;
  addSpotlight?: Maybe<HomeData>;
  addWatchList?: Maybe<WatchList>;
  /** Superadmin/Admin login */
  adminLogin?: Maybe<AuthResponse>;
  adminLogout?: Maybe<SuccessResponse>;
  /** Create admin user. Only super admin can perform this action */
  createAdmin?: Maybe<Admin>;
  createLiveContent: Live;
  /** Create movie */
  createMovie: Movie;
  /** Create a subscription plan */
  createPlan?: Maybe<SubscriptionPlan>;
  /** Create a new TV show */
  createTVShow?: Maybe<TvShow>;
  /** Create episode for a particular season for a particular tv show */
  createTVShowEpisode: TvShowEpisodes;
  /** Create season for a particular TV Show */
  createTVShowSeason?: Maybe<TvShowSeason>;
  /** Delete admin user. Only super admin can perform this action */
  deleteAdmin?: Maybe<Admin>;
  /** Delete home data along with the home data list */
  deleteHomeData?: Maybe<HomeData>;
  deleteItemFromHomeDataList?: Maybe<HomeDataList>;
  deleteLiveContent?: Maybe<Live>;
  deleteMovie?: Maybe<Movie>;
  deleteMyAccount?: Maybe<SuccessResponse>;
  deleteMyWatchList?: Maybe<WatchList>;
  deletePlan?: Maybe<SubscriptionPlan>;
  deleteTVShow?: Maybe<TvShow>;
  deleteTVShowEpisode?: Maybe<TvShowEpisodes>;
  deleteTVShowSeason?: Maybe<TvShowSeason>;
  endLiveStream?: Maybe<Live>;
  generateContentUrl?: Maybe<GenerateUrlResponse>;
  getHomeDataById: HomeDataListPaginator;
  issueRefund?: Maybe<SuccessResponse>;
  rentLive?: Maybe<PaymentResponse>;
  /** Rent a movie */
  rentMovie?: Maybe<PaymentResponse>;
  rentMultipleEpisodes?: Maybe<PaymentResponse>;
  /** Rent tv show episode */
  rentTVShowEpisode?: Maybe<PaymentResponse>;
  /** Can only be called by superadmin or support. Reset user login session to solve multiple account login attempt error that may arise for some reason */
  resetUserSession?: Maybe<User>;
  sendPushNotification?: Maybe<SuccessResponse>;
  subscribeToPlan?: Maybe<PaymentResponse>;
  subscriptionPlanList: SubscriptionPlanPaginator;
  /** Update admin user. Only super admin can perform this action */
  updateAdmin?: Maybe<Admin>;
  /** Update home data category order. Order is calculated as ascending  */
  updateHomeDataCategory?: Maybe<HomeData>;
  updateLiveContent?: Maybe<Live>;
  /** Update movie based on ID */
  updateMovie?: Maybe<Movie>;
  /** Update auth user info */
  updateMyInfo?: Maybe<User>;
  /** Update subscription plan */
  updatePlan?: Maybe<SubscriptionPlan>;
  updateTVShow?: Maybe<TvShow>;
  updateTVShowEpisode: TvShowEpisodes;
  updateTVShowSeason?: Maybe<TvShowSeason>;
  updateUserSubscriptionStatus?: Maybe<User>;
  upsertContentTrailer?: Maybe<TrailerUpsertResponse>;
  /** Update or create fcm token linked to currently auth user */
  upsertFcmToken?: Maybe<SuccessResponse>;
  upsertWatchedDuration?: Maybe<SuccessResponse>;
  /** User login. OTP will be sent to 'mobile_number' */
  userLogin?: Maybe<LoginOtpResponse>;
  /** Verify OTP for login */
  userLoginVerifyOTP?: Maybe<UserResponse>;
  userLogout?: Maybe<SuccessResponse>;
  verifyIOSContentPurchase: Scalars['Boolean']['output'];
};


export type MutationAddHomeDataArgs = {
  category: Scalars['String']['input'];
  display_type?: InputMaybe<DisplayType>;
  order?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationAddHomeDataListArgs = {
  content_id: Scalars['ID']['input'];
  content_type: ContentType;
  home_data_id: Scalars['ID']['input'];
};


export type MutationAddSpotlightArgs = {
  content_id: Scalars['ID']['input'];
  content_type: ContentType;
  poster_image: Scalars['Upload']['input'];
  poster_image_landscape: Scalars['Upload']['input'];
  title_image?: InputMaybe<Scalars['Upload']['input']>;
};


export type MutationAddWatchListArgs = {
  content_type?: InputMaybe<WatchListableType>;
  id: Scalars['ID']['input'];
};


export type MutationAdminLoginArgs = {
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};


export type MutationCreateAdminArgs = {
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};


export type MutationCreateLiveContentArgs = {
  description_en?: InputMaybe<Scalars['String']['input']>;
  description_mz?: InputMaybe<Scalars['String']['input']>;
  for_subscriber?: InputMaybe<Scalars['Boolean']['input']>;
  image_landscape?: InputMaybe<Scalars['Upload']['input']>;
  image_portrait?: InputMaybe<Scalars['Upload']['input']>;
  is_free: Scalars['Boolean']['input'];
  is_published?: InputMaybe<Scalars['Boolean']['input']>;
  price: Scalars['Float']['input'];
  search_tags: Array<Scalars['String']['input']>;
  title: Scalars['String']['input'];
};


export type MutationCreateMovieArgs = {
  age_label?: InputMaybe<Scalars['String']['input']>;
  casts?: InputMaybe<Scalars['String']['input']>;
  description_en?: InputMaybe<Scalars['String']['input']>;
  description_mz?: InputMaybe<Scalars['String']['input']>;
  duration: Scalars['Int']['input'];
  for_subscriber?: InputMaybe<Scalars['Boolean']['input']>;
  genre?: InputMaybe<Scalars['String']['input']>;
  image_landscape?: InputMaybe<Scalars['Upload']['input']>;
  image_portrait?: InputMaybe<Scalars['Upload']['input']>;
  image_portrait2x?: InputMaybe<Scalars['Upload']['input']>;
  image_square?: InputMaybe<Scalars['Upload']['input']>;
  image_square2x?: InputMaybe<Scalars['Upload']['input']>;
  is_free?: InputMaybe<Scalars['Boolean']['input']>;
  is_published: Scalars['Boolean']['input'];
  price: Scalars['Float']['input'];
  production_year?: InputMaybe<Scalars['String']['input']>;
  search_tags: Array<Scalars['String']['input']>;
  subtitle_file_en?: InputMaybe<Scalars['Upload']['input']>;
  subtitle_file_mz?: InputMaybe<Scalars['Upload']['input']>;
  title: Scalars['String']['input'];
  ttl: Scalars['Int']['input'];
};


export type MutationCreatePlanArgs = {
  duration: Scalars['Int']['input'];
  name: Scalars['String']['input'];
  price: Scalars['Float']['input'];
};


export type MutationCreateTvShowArgs = {
  age_label?: InputMaybe<Scalars['String']['input']>;
  casts?: InputMaybe<Scalars['String']['input']>;
  description_en?: InputMaybe<Scalars['String']['input']>;
  description_mz?: InputMaybe<Scalars['String']['input']>;
  genre?: InputMaybe<Scalars['String']['input']>;
  image_landscape?: InputMaybe<Scalars['Upload']['input']>;
  image_portrait?: InputMaybe<Scalars['Upload']['input']>;
  image_portrait2x?: InputMaybe<Scalars['Upload']['input']>;
  image_square?: InputMaybe<Scalars['Upload']['input']>;
  image_square2x?: InputMaybe<Scalars['Upload']['input']>;
  price_per_episode: Scalars['Float']['input'];
  production_year?: InputMaybe<Scalars['String']['input']>;
  search_tags: Array<Scalars['String']['input']>;
  title: Scalars['String']['input'];
  ttl_episode: Scalars['Int']['input'];
  ttl_season: Scalars['Int']['input'];
};


export type MutationCreateTvShowEpisodeArgs = {
  description_en?: InputMaybe<Scalars['String']['input']>;
  description_mz?: InputMaybe<Scalars['String']['input']>;
  duration: Scalars['Int']['input'];
  for_subscriber?: InputMaybe<Scalars['Boolean']['input']>;
  guest_starring?: InputMaybe<Scalars['String']['input']>;
  image_landscape?: InputMaybe<Scalars['Upload']['input']>;
  image_portrait?: InputMaybe<Scalars['Upload']['input']>;
  is_free?: InputMaybe<Scalars['Boolean']['input']>;
  is_published: Scalars['Boolean']['input'];
  sub_header: Scalars['String']['input'];
  subtitle_file_en?: InputMaybe<Scalars['Upload']['input']>;
  subtitle_file_mz?: InputMaybe<Scalars['Upload']['input']>;
  title: Scalars['String']['input'];
  tv_show_id: Scalars['ID']['input'];
  tv_show_season_id: Scalars['ID']['input'];
};


export type MutationCreateTvShowSeasonArgs = {
  description_en?: InputMaybe<Scalars['String']['input']>;
  description_mz?: InputMaybe<Scalars['String']['input']>;
  title: Scalars['String']['input'];
  tv_show_id: Scalars['ID']['input'];
};


export type MutationDeleteAdminArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteHomeDataArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteItemFromHomeDataListArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteLiveContentArgs = {
  id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationDeleteMovieArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteMyWatchListArgs = {
  id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationDeletePlanArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteTvShowArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteTvShowEpisodeArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteTvShowSeasonArgs = {
  id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationEndLiveStreamArgs = {
  id: Scalars['ID']['input'];
};


export type MutationGenerateContentUrlArgs = {
  content_type: WatchableContentTypes;
  id: Scalars['ID']['input'];
};


export type MutationGetHomeDataByIdArgs = {
  first: Scalars['Int']['input'];
  home_data_id: Scalars['ID']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationIssueRefundArgs = {
  order_id: Scalars['String']['input'];
  payment_provider: PaymentProvider;
  refund_amount?: InputMaybe<Scalars['Float']['input']>;
};


export type MutationRentLiveArgs = {
  id: Scalars['ID']['input'];
  redirect_url?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRentMovieArgs = {
  id: Scalars['ID']['input'];
  redirect_url?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRentMultipleEpisodesArgs = {
  ids: Array<Scalars['ID']['input']>;
  total_amount: Scalars['Float']['input'];
};


export type MutationRentTvShowEpisodeArgs = {
  id: Scalars['ID']['input'];
  redirect_url?: InputMaybe<Scalars['String']['input']>;
};


export type MutationResetUserSessionArgs = {
  user_id: Scalars['ID']['input'];
};


export type MutationSendPushNotificationArgs = {
  body: Scalars['String']['input'];
  clickable_grand_parent_id?: InputMaybe<Scalars['Int']['input']>;
  clickable_id?: InputMaybe<Scalars['Int']['input']>;
  clickable_parent_id?: InputMaybe<Scalars['Int']['input']>;
  clickable_type?: InputMaybe<NotificationClickableType>;
  title: Scalars['String']['input'];
};


export type MutationSubscribeToPlanArgs = {
  plan_id: Scalars['ID']['input'];
  redirect_url?: InputMaybe<Scalars['String']['input']>;
};


export type MutationSubscriptionPlanListArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationUpdateAdminArgs = {
  id: Scalars['ID']['input'];
  password?: InputMaybe<Scalars['String']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateHomeDataCategoryArgs = {
  category: Scalars['String']['input'];
  display_type?: InputMaybe<DisplayType>;
  id: Scalars['ID']['input'];
  order: Scalars['Int']['input'];
};


export type MutationUpdateLiveContentArgs = {
  description_en?: InputMaybe<Scalars['String']['input']>;
  description_mz?: InputMaybe<Scalars['String']['input']>;
  for_subscriber?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['ID']['input'];
  image_landscape?: InputMaybe<Scalars['Upload']['input']>;
  image_portrait?: InputMaybe<Scalars['Upload']['input']>;
  is_free?: InputMaybe<Scalars['Boolean']['input']>;
  is_published?: InputMaybe<Scalars['Boolean']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
  producer_share?: InputMaybe<Scalars['Float']['input']>;
  search_tags?: InputMaybe<Array<Scalars['String']['input']>>;
  title?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateMovieArgs = {
  age_label?: InputMaybe<Scalars['String']['input']>;
  casts?: InputMaybe<Scalars['String']['input']>;
  description_en?: InputMaybe<Scalars['String']['input']>;
  description_mz?: InputMaybe<Scalars['String']['input']>;
  duration?: InputMaybe<Scalars['Int']['input']>;
  for_subscriber?: InputMaybe<Scalars['Boolean']['input']>;
  genre?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  image_landscape?: InputMaybe<Scalars['Upload']['input']>;
  image_portrait?: InputMaybe<Scalars['Upload']['input']>;
  image_portrait2x?: InputMaybe<Scalars['Upload']['input']>;
  image_square?: InputMaybe<Scalars['Upload']['input']>;
  image_square2x?: InputMaybe<Scalars['Upload']['input']>;
  is_free?: InputMaybe<Scalars['Boolean']['input']>;
  is_published: Scalars['Boolean']['input'];
  price?: InputMaybe<Scalars['Float']['input']>;
  producer_share?: InputMaybe<Scalars['Float']['input']>;
  production_year?: InputMaybe<Scalars['String']['input']>;
  search_tags?: InputMaybe<Array<Scalars['String']['input']>>;
  subtitle_file_en?: InputMaybe<Scalars['Upload']['input']>;
  subtitle_file_mz?: InputMaybe<Scalars['Upload']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
  title_image?: InputMaybe<Scalars['Upload']['input']>;
  ttl?: InputMaybe<Scalars['Int']['input']>;
  upload_new_video?: InputMaybe<Scalars['Boolean']['input']>;
};


export type MutationUpdateMyInfoArgs = {
  language?: InputMaybe<Scalars['String']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdatePlanArgs = {
  duration?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
};


export type MutationUpdateTvShowArgs = {
  age_label?: InputMaybe<Scalars['String']['input']>;
  casts?: InputMaybe<Scalars['String']['input']>;
  description_en?: InputMaybe<Scalars['String']['input']>;
  description_mz?: InputMaybe<Scalars['String']['input']>;
  genre?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  image_landscape?: InputMaybe<Scalars['Upload']['input']>;
  image_portrait?: InputMaybe<Scalars['Upload']['input']>;
  image_portrait2x?: InputMaybe<Scalars['Upload']['input']>;
  image_square?: InputMaybe<Scalars['Upload']['input']>;
  image_square2x?: InputMaybe<Scalars['Upload']['input']>;
  price_per_episode?: InputMaybe<Scalars['Float']['input']>;
  producer_share?: InputMaybe<Scalars['Float']['input']>;
  production_year?: InputMaybe<Scalars['String']['input']>;
  search_tags?: InputMaybe<Array<Scalars['String']['input']>>;
  title?: InputMaybe<Scalars['String']['input']>;
  ttl_episode?: InputMaybe<Scalars['Int']['input']>;
  ttl_season?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationUpdateTvShowEpisodeArgs = {
  description_en?: InputMaybe<Scalars['String']['input']>;
  description_mz?: InputMaybe<Scalars['String']['input']>;
  duration?: InputMaybe<Scalars['Int']['input']>;
  for_subscriber?: InputMaybe<Scalars['Boolean']['input']>;
  guest_starring?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  image_landscape?: InputMaybe<Scalars['Upload']['input']>;
  image_portrait?: InputMaybe<Scalars['Upload']['input']>;
  is_free?: InputMaybe<Scalars['Boolean']['input']>;
  is_published?: InputMaybe<Scalars['Boolean']['input']>;
  sub_header?: InputMaybe<Scalars['String']['input']>;
  subtitle_file_en?: InputMaybe<Scalars['Upload']['input']>;
  subtitle_file_mz?: InputMaybe<Scalars['Upload']['input']>;
  title: Scalars['String']['input'];
  tv_show_id: Scalars['ID']['input'];
  tv_show_season_id: Scalars['ID']['input'];
  upload_new_video?: InputMaybe<Scalars['Boolean']['input']>;
};


export type MutationUpdateTvShowSeasonArgs = {
  description_en?: InputMaybe<Scalars['String']['input']>;
  description_mz?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  subtitle_path_en?: InputMaybe<Scalars['Upload']['input']>;
  subtitle_path_mz?: InputMaybe<Scalars['Upload']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateUserSubscriptionStatusArgs = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  status: Scalars['String']['input'];
  user_id: Scalars['ID']['input'];
};


export type MutationUpsertContentTrailerArgs = {
  content_id: Scalars['ID']['input'];
  trailerable_type: TrailerableType;
};


export type MutationUpsertFcmTokenArgs = {
  token: Scalars['String']['input'];
};


export type MutationUpsertWatchedDurationArgs = {
  content_id: Scalars['ID']['input'];
  content_type: WatchDurationContentType;
  duration: Scalars['Int']['input'];
  total_duration: Scalars['Int']['input'];
};


export type MutationUserLoginArgs = {
  email?: InputMaybe<Scalars['String']['input']>;
  force_reset_session?: InputMaybe<Scalars['Boolean']['input']>;
  mobile_number?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUserLoginVerifyOtpArgs = {
  code: Scalars['Int']['input'];
  otp_id: Scalars['Int']['input'];
  username?: InputMaybe<Scalars['String']['input']>;
};


export type MutationVerifyIosContentPurchaseArgs = {
  local_verification_data: Scalars['String']['input'];
  purchase_type: IosPurchaseType;
};

export enum NotificationClickableType {
  Live = 'LIVE',
  Movie = 'MOVIE',
  TvShow = 'TV_SHOW',
  TvShowEpisode = 'TV_SHOW_EPISODE',
  TvShowSeason = 'TV_SHOW_SEASON'
}

/** Allows ordering a list of records. */
export type OrderByClause = {
  /** The column that is used for ordering. */
  column: Scalars['String']['input'];
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Aggregate functions when ordering by a relation without specifying a column. */
export enum OrderByRelationAggregateFunction {
  /** Amount of items. */
  Count = 'COUNT'
}

/** Aggregate functions when ordering by a relation that may specify a column. */
export enum OrderByRelationWithColumnAggregateFunction {
  /** Average. */
  Avg = 'AVG',
  /** Amount of items. */
  Count = 'COUNT',
  /** Maximum. */
  Max = 'MAX',
  /** Minimum. */
  Min = 'MIN',
  /** Sum. */
  Sum = 'SUM'
}

export type Otp = {
  __typename?: 'Otp';
  code: Scalars['String']['output'];
  id: Scalars['ID']['output'];
};

/** Information about pagination using a fully featured paginator. */
export type PaginatorInfo = {
  __typename?: 'PaginatorInfo';
  /** Number of items in the current page. */
  count: Scalars['Int']['output'];
  /** Index of the current page. */
  currentPage: Scalars['Int']['output'];
  /** Index of the first item in the current page. */
  firstItem?: Maybe<Scalars['Int']['output']>;
  /** Are there more pages after this one? */
  hasMorePages: Scalars['Boolean']['output'];
  /** Index of the last item in the current page. */
  lastItem?: Maybe<Scalars['Int']['output']>;
  /** Index of the last available page. */
  lastPage: Scalars['Int']['output'];
  /** Number of items per page. */
  perPage: Scalars['Int']['output'];
  /** Number of total available items. */
  total: Scalars['Int']['output'];
};

export type PaymentOrder = {
  __typename?: 'PaymentOrder';
  id: Scalars['ID']['output'];
  paymentable?: Maybe<PaymentableOrder>;
  paymentable_id: Scalars['Int']['output'];
  paymentable_type: Scalars['String']['output'];
};

export enum PaymentProvider {
  Phonepe = 'PHONEPE',
  Rzpay = 'RZPAY'
}

export type PaymentResponse = PhonePePaymentResponse | RazorPayPaymentResponse;

export type PaymentStatusResponse = {
  __typename?: 'PaymentStatusResponse';
  /** Possible values are `success` | `fail` | `pending` | `error`. See README.md-`Payment Status` for more info */
  status?: Maybe<Scalars['String']['output']>;
};

export type PaymentableOrder = ApplePaymentOrder | PhonePePaymentOrder | RzpayPaymentOrder;

export type PhonePePaymentOrder = {
  __typename?: 'PhonePePaymentOrder';
  id: Scalars['ID']['output'];
  order_id: Scalars['String']['output'];
  payment_error?: Maybe<Scalars['String']['output']>;
  payment_id?: Maybe<Scalars['String']['output']>;
  payment_instrument_type?: Maybe<Scalars['String']['output']>;
  payment_status: Scalars['String']['output'];
  refund_amount?: Maybe<Scalars['Float']['output']>;
  refund_error?: Maybe<Scalars['String']['output']>;
  refund_status?: Maybe<Scalars['String']['output']>;
  transaction_id?: Maybe<Scalars['String']['output']>;
};

export type PhonePePaymentResponse = {
  __typename?: 'PhonePePaymentResponse';
  goto_url: Scalars['String']['output'];
};

/** Indicates what fields are available at the top level of a query operation. */
export type Query = {
  __typename?: 'Query';
  admins?: Maybe<Array<Maybe<Admin>>>;
  /** Get paginated list of home category (think `view more`) */
  categoryById: HomeDataListPaginator;
  checkContentRentStatus?: Maybe<Rentable>;
  /** @deprecated No longer supported */
  getBasicStats?: Maybe<BasicStats>;
  getContentReport?: Maybe<ContentReportResponse>;
  /** returns 10 most recent continue watching list */
  getContinueWatchingList?: Maybe<Array<WatchHistory>>;
  getHomeData: GetHomeDataPaginator;
  getLiveBySlug?: Maybe<Live>;
  getLiveStreamHealth?: Maybe<LiveStreamHealthResponse>;
  getLives?: Maybe<GetLivesPaginator>;
  /** Find a single user by an identifying attribute. */
  getMe?: Maybe<User>;
  getMovieBySlug?: Maybe<Movie>;
  getMovies?: Maybe<GetMoviesPaginator>;
  /** Get transaction status */
  getPaymentStatus?: Maybe<PaymentStatusResponse>;
  getRevenue: Revenue;
  getTvShowBySlug?: Maybe<TvShow>;
  getTvShowReportForProducer?: Maybe<Array<Maybe<TvShowReportResponse>>>;
  getTvShows?: Maybe<GetTvShowsPaginator>;
  /** Fetch user list */
  getUsers: UserPaginator;
  getViewerCount?: Maybe<ViewerCount>;
  isAddedToWatchList?: Maybe<IsAddedToWatchListResponse>;
  liveContentById?: Maybe<Live>;
  liveContentList: LivePaginator;
  movieById?: Maybe<Movie>;
  movies: MoviePaginator;
  /** Purchase history */
  myContentRentList: UserRentContentPaginator;
  myWatchList: WatchListPaginator;
  searchContent?: Maybe<Array<Maybe<SearchableContent>>>;
  subscriptionPlans?: Maybe<Array<Maybe<SubscriptionPlan>>>;
  tvShowById?: Maybe<TvShow>;
  tvShowEpisodeById?: Maybe<TvShowEpisodes>;
  tvShowSeasonById?: Maybe<TvShowSeason>;
  tvShows: TvShowPaginator;
  userList: UserListPaginator;
  /** only for CMS */
  userPurchaseHistories: UserRentContentPaginator;
  /** Not to be used in CMS. see `@whereAuth` */
  userSubscription: UserSubscriptionPaginator;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryAdminsArgs = {
  onlySupport?: InputMaybe<Scalars['Boolean']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryCategoryByIdArgs = {
  first: Scalars['Int']['input'];
  home_data_id: Scalars['ID']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryCheckContentRentStatusArgs = {
  content_id: Scalars['ID']['input'];
  content_type: RentableContentType;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetBasicStatsArgs = {
  content_type: RentableContentType;
  stats_slug: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetContentReportArgs = {
  content_type: ReportableType;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  slug: Scalars['String']['input'];
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetHomeDataArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetLiveBySlugArgs = {
  slug: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetLiveStreamHealthArgs = {
  live_stream_id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetLivesArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  keyword?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetMovieBySlugArgs = {
  slug: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetMoviesArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  keyword?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetPaymentStatusArgs = {
  order_id: Scalars['String']['input'];
  provider: PaymentProvider;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetRevenueArgs = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  payment_type?: InputMaybe<Scalars['String']['input']>;
  revenue_type?: InputMaybe<RevenueType>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetTvShowBySlugArgs = {
  slug: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetTvShowReportForProducerArgs = {
  slug: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetTvShowsArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  is_free?: InputMaybe<Scalars['Boolean']['input']>;
  keyword?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetUsersArgs = {
  first: Scalars['Int']['input'];
  mobile_number?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetViewerCountArgs = {
  cdn_id: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryIsAddedToWatchListArgs = {
  content_id: Scalars['ID']['input'];
  content_type: WatchListableType;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryLiveContentByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryLiveContentListArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryMovieByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryMoviesArgs = {
  description_en?: InputMaybe<Scalars['String']['input']>;
  description_mz?: InputMaybe<Scalars['String']['input']>;
  first?: Scalars['Int']['input'];
  is_free?: InputMaybe<Scalars['Boolean']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryMyContentRentListArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryMyWatchListArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QuerySearchContentArgs = {
  keyword: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryTvShowByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryTvShowEpisodeByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryTvShowSeasonByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryTvShowsArgs = {
  description_en?: InputMaybe<Scalars['String']['input']>;
  description_mz?: InputMaybe<Scalars['String']['input']>;
  first?: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryUserListArgs = {
  first: Scalars['Int']['input'];
  keyword?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  user_type?: InputMaybe<UserTypeFilter>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryUserPurchaseHistoriesArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  userId: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryUserSubscriptionArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  subscription_status?: InputMaybe<Scalars['String']['input']>;
};

export type RazorPayPaymentResponse = {
  __typename?: 'RazorPayPaymentResponse';
  order_id: Scalars['String']['output'];
  price: Scalars['Float']['output'];
};

export type Rentable = Live | Movie | TvShowEpisodes | TvShowSeason;

export enum RentableContentType {
  Live = 'LIVE',
  Movie = 'MOVIE',
  TvShowEpisode = 'TV_SHOW_EPISODE',
  /** @deprecated Season is no longer rentable */
  TvShowSeason = 'TV_SHOW_SEASON'
}

export enum ReportableType {
  Live = 'LIVE',
  Movie = 'MOVIE',
  TvShowSeason = 'TV_SHOW_SEASON'
}

export type Revenue = {
  __typename?: 'Revenue';
  purchase_count: Scalars['Int']['output'];
  total_revenue: Scalars['Float']['output'];
};

export enum RevenueType {
  Content = 'CONTENT',
  Subscription = 'SUBSCRIPTION'
}

export type RzpayPaymentOrder = {
  __typename?: 'RzpayPaymentOrder';
  id: Scalars['ID']['output'];
  order_id: Scalars['String']['output'];
  payment_id?: Maybe<Scalars['String']['output']>;
  payment_status: Scalars['String']['output'];
  refund_amount?: Maybe<Scalars['Float']['output']>;
  refund_error?: Maybe<Scalars['String']['output']>;
  refund_status?: Maybe<Scalars['String']['output']>;
};

export type Searchable = Live | Movie | TvShow;

export type SearchableContent = {
  __typename?: 'SearchableContent';
  canSearch?: Maybe<Searchable>;
  id: Scalars['ID']['output'];
  searchable_id: Scalars['ID']['output'];
};

export enum SearchableContentType {
  Movie = 'MOVIE',
  TvShow = 'TV_SHOW'
}

/** Directions for ordering a list of records. */
export enum SortOrder {
  /** Sort records in ascending order. */
  Asc = 'ASC',
  /** Sort records in descending order. */
  Desc = 'DESC'
}

export type StatsData = {
  __typename?: 'StatsData';
  purchase_count: Scalars['Int']['output'];
  title: Scalars['String']['output'];
  total_revenue: Scalars['Float']['output'];
};

/** Subscription plan that a user can subscribe to */
export type SubscriptionPlan = {
  __typename?: 'SubscriptionPlan';
  duration: Scalars['Int']['output'];
  iap_product_id?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  price: Scalars['Float']['output'];
};

/** A paginated list of SubscriptionPlan items. */
export type SubscriptionPlanPaginator = {
  __typename?: 'SubscriptionPlanPaginator';
  /** A list of SubscriptionPlan items. */
  data: Array<SubscriptionPlan>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

/** General success response */
export type SuccessResponse = {
  __typename?: 'SuccessResponse';
  message: Scalars['String']['output'];
};

export type TvShow = {
  __typename?: 'TVShow';
  age_label?: Maybe<Scalars['String']['output']>;
  casts?: Maybe<Scalars['String']['output']>;
  description_en?: Maybe<Scalars['String']['output']>;
  description_mz?: Maybe<Scalars['String']['output']>;
  genre?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  /** Landscape image poster */
  imageLandscape?: Maybe<AppImage>;
  /** Portrait image poster */
  imagePortrait?: Maybe<AppImage>;
  imagePortrait2x?: Maybe<AppImage>;
  imageSquare?: Maybe<AppImage>;
  imageSquare2x?: Maybe<AppImage>;
  price_per_episode: Scalars['Float']['output'];
  /** Amount (in percentage) */
  producer_share: Scalars['Float']['output'];
  production_year?: Maybe<Scalars['String']['output']>;
  search_tags?: Maybe<Array<Scalars['String']['output']>>;
  seasons?: Maybe<Array<Maybe<TvShowSeason>>>;
  slug: Scalars['String']['output'];
  subtitle_path?: Maybe<Scalars['String']['output']>;
  title: Scalars['String']['output'];
  ttl_episode: Scalars['Int']['output'];
  ttl_season: Scalars['Int']['output'];
};

export type TvShowEpisodes = {
  __typename?: 'TVShowEpisodes';
  cdn_content_status?: Maybe<Scalars['String']['output']>;
  cdn_id?: Maybe<Scalars['String']['output']>;
  cdn_playback_id?: Maybe<Scalars['String']['output']>;
  cdn_upload_url?: Maybe<Scalars['String']['output']>;
  /** Note: Use sparringly */
  continueWatching?: Maybe<WatchHistory>;
  created_at: Scalars['String']['output'];
  description_en?: Maybe<Scalars['String']['output']>;
  description_mz?: Maybe<Scalars['String']['output']>;
  duration: Scalars['Int']['output'];
  for_subscriber: Scalars['Boolean']['output'];
  guest_starring?: Maybe<Scalars['String']['output']>;
  /** apple in app purchase id */
  iap_product_id?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  imageLandscape?: Maybe<AppImage>;
  imagePortrait?: Maybe<AppImage>;
  /** Check if auth user has added this content to watch list. If unauthenticated, this will return false. Note: Use Sparringly. Do not use this property when fetching list of episodes */
  isAddedToWatchList: Scalars['Int']['output'];
  is_free: Scalars['Boolean']['output'];
  is_published: Scalars['Boolean']['output'];
  /** Note: Do not use this property when fetch list to preserve server resource. Use only for fetching detail content. Explicity nullable. If true, user has rented. if false or null, user has not rented the content */
  is_rented?: Maybe<Scalars['Boolean']['output']>;
  season?: Maybe<TvShowSeason>;
  sub_header: Scalars['String']['output'];
  subtitle_path_en?: Maybe<Scalars['String']['output']>;
  subtitle_path_mz?: Maybe<Scalars['String']['output']>;
  title: Scalars['String']['output'];
  trailer?: Maybe<ContentTrailer>;
  tvShow?: Maybe<TvShow>;
  tv_show_id: Scalars['ID']['output'];
  tv_show_season_id: Scalars['ID']['output'];
};

/** A paginated list of TVShow items. */
export type TvShowPaginator = {
  __typename?: 'TVShowPaginator';
  /** A list of TVShow items. */
  data: Array<TvShow>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type TvShowReportResponse = {
  __typename?: 'TVShowReportResponse';
  episode_title: Scalars['String']['output'];
  purchase_count: Scalars['Int']['output'];
  /** Including platform fee 2% */
  total_revenue: Scalars['Float']['output'];
};

export type TvShowSeason = {
  __typename?: 'TVShowSeason';
  description_en?: Maybe<Scalars['String']['output']>;
  description_mz?: Maybe<Scalars['String']['output']>;
  episodes?: Maybe<Array<Maybe<TvShowEpisodes>>>;
  id: Scalars['ID']['output'];
  /** Check if auth user has added this content to watch list. If unauthenticated, this will return false. Note: Use Sparringly. Do not use this property when fetching list of episodes */
  isAddedToWatchList: Scalars['Int']['output'];
  /** Note: Do not use this property when fetch list to preserve server resource. Use only for fetching detail content. Explicity nullable. If true, user has rented. if false or null, user has not rented the content */
  is_rented?: Maybe<Scalars['Boolean']['output']>;
  /** Possible values: `free`,`for_subscriber`,`rent` */
  rentable_status?: Maybe<Scalars['String']['output']>;
  slug: Scalars['String']['output'];
  title: Scalars['String']['output'];
  trailer?: Maybe<ContentTrailer>;
  tvShow?: Maybe<TvShow>;
  tv_show_id: Scalars['ID']['output'];
};

export type TrailerUpsertResponse = {
  __typename?: 'TrailerUpsertResponse';
  cdn_trailer_upload_url: Scalars['String']['output'];
};

export enum TrailerableType {
  Movie = 'MOVIE',
  TvShowEpisode = 'TV_SHOW_EPISODE',
  TvShowSeason = 'TV_SHOW_SEASON'
}

/** Specify if you want to include or exclude trashed results from a query. */
export enum Trashed {
  /** Only return trashed results. */
  Only = 'ONLY',
  /** Return both trashed and non-trashed results. */
  With = 'WITH',
  /** Only return non-trashed results. */
  Without = 'WITHOUT'
}

export type TvShowAppPaginatorInfo = {
  __typename?: 'TvShowAppPaginatorInfo';
  currentPage: Scalars['Int']['output'];
  hasMorePages: Scalars['Boolean']['output'];
  lastPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

/** Account of a person who utilizes this application. */
export type User = {
  __typename?: 'User';
  /** When the account was created. */
  created_at: Scalars['DateTime']['output'];
  email?: Maybe<Scalars['String']['output']>;
  /** Unique primary key. */
  id: Scalars['ID']['output'];
  /** Preferred language: en | mz . Default value is 'en' */
  language: Scalars['String']['output'];
  mobile_number?: Maybe<Scalars['String']['output']>;
  subscription?: Maybe<UserSubscription>;
  /** When the account was last updated. */
  updated_at: Scalars['DateTime']['output'];
  /** Non-unique name. */
  username?: Maybe<Scalars['String']['output']>;
  verified_at?: Maybe<Scalars['DateTime']['output']>;
};

export type UserListPaginator = {
  __typename?: 'UserListPaginator';
  data?: Maybe<Array<User>>;
  paginatorInfo: AppPaginatorInfo2;
};

/** A paginated list of User items. */
export type UserPaginator = {
  __typename?: 'UserPaginator';
  /** A list of User items. */
  data: Array<User>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type UserRentContent = {
  __typename?: 'UserRentContent';
  end_date?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  paymentOrder?: Maybe<PaymentOrder>;
  price: Scalars['Float']['output'];
  rentable?: Maybe<Rentable>;
  start_date?: Maybe<Scalars['DateTime']['output']>;
  status: Scalars['String']['output'];
  user_id: Scalars['ID']['output'];
};

/** A paginated list of UserRentContent items. */
export type UserRentContentPaginator = {
  __typename?: 'UserRentContentPaginator';
  /** A list of UserRentContent items. */
  data: Array<UserRentContent>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type UserResponse = {
  __typename?: 'UserResponse';
  token?: Maybe<Scalars['String']['output']>;
  user?: Maybe<User>;
};

/** User subscription to a particular plan */
export type UserSubscription = {
  __typename?: 'UserSubscription';
  amount_paid: Scalars['Float']['output'];
  end_date?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  paymentOrder?: Maybe<PaymentOrder>;
  pg_type: Scalars['String']['output'];
  plan?: Maybe<SubscriptionPlan>;
  plan_id: Scalars['ID']['output'];
  start_date?: Maybe<Scalars['DateTime']['output']>;
  /** Possible values: `initated` | `active` | `expired` */
  subscription_status?: Maybe<Scalars['String']['output']>;
  user?: Maybe<User>;
  user_id: Scalars['ID']['output'];
};

/** A paginated list of UserSubscription items. */
export type UserSubscriptionPaginator = {
  __typename?: 'UserSubscriptionPaginator';
  /** A list of UserSubscription items. */
  data: Array<UserSubscription>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export enum UserTypeFilter {
  Nonsubscriber = 'NONSUBSCRIBER',
  Subscriber = 'SUBSCRIBER'
}

export type ViewerCount = {
  __typename?: 'ViewerCount';
  viewers: Scalars['Int']['output'];
  views: Scalars['Int']['output'];
};

export enum WatchDurationContentType {
  Movie = 'MOVIE',
  TvShowEpisode = 'TV_SHOW_EPISODE'
}

export type WatchHistory = {
  __typename?: 'WatchHistory';
  id: Scalars['ID']['output'];
  total_duration: Scalars['Int']['output'];
  user_id: Scalars['ID']['output'];
  watchable?: Maybe<ContinueWatchable>;
  watchable_id: Scalars['ID']['output'];
  watchable_type: Scalars['String']['output'];
  watched_duration: Scalars['Int']['output'];
};

export type WatchList = {
  __typename?: 'WatchList';
  id: Scalars['ID']['output'];
  user_id: Scalars['ID']['output'];
  watch_listable?: Maybe<WatchListable>;
};

/** A paginated list of WatchList items. */
export type WatchListPaginator = {
  __typename?: 'WatchListPaginator';
  /** A list of WatchList items. */
  data: Array<WatchList>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type WatchListable = Movie | TvShow | TvShowEpisodes | TvShowSeason;

export enum WatchListableType {
  Movie = 'MOVIE',
  TvShowEpisode = 'TV_SHOW_EPISODE',
  TvShowSeason = 'TV_SHOW_SEASON'
}

export enum WatchableContentTypes {
  Live = 'LIVE',
  Movie = 'MOVIE',
  Trailer = 'TRAILER',
  TvShowEpisode = 'TV_SHOW_EPISODE'
}

export type RentEpisodeMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  redirectURL?: InputMaybe<Scalars['String']['input']>;
}>;


export type RentEpisodeMutation = { __typename?: 'Mutation', rentTVShowEpisode?: { __typename: 'PhonePePaymentResponse', goto_url: string } | { __typename: 'RazorPayPaymentResponse', order_id: string } | null };

export type ContinueWatchingFragmentFragment = { __typename?: 'WatchHistory', id: string, watchable_id: string, watchable_type: string, watched_duration: number, total_duration: number, user_id: string, watchable?: (
    { __typename: 'Movie' }
    & { ' $fragmentRefs'?: { 'MovieFragmentFragment': MovieFragmentFragment } }
  ) | (
    { __typename: 'TVShowEpisodes' }
    & { ' $fragmentRefs'?: { 'TvEpisodeFragmentFragment': TvEpisodeFragmentFragment } }
  ) | null } & { ' $fragmentName'?: 'ContinueWatchingFragmentFragment' };

export type TvEpisodeFragmentFragment = { __typename: 'TVShowEpisodes', id: string, cdn_playback_id?: string | null, isAddedToWatchList: number, title: string, sub_header: string, duration: number, is_rented?: boolean | null, is_free: boolean, description_en?: string | null, description_mz?: string | null, for_subscriber: boolean, tv_show_id: string, tv_show_season_id: string, continueWatching?: { __typename?: 'WatchHistory', watched_duration: number, total_duration: number } | null, imageLandscape?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null } & { ' $fragmentName'?: 'TvEpisodeFragmentFragment' };

export type HomeDataFragmentFragment = { __typename?: 'HomeData', id: string, category: string, order: number, display_type?: string | null, homeDataList?: Array<{ __typename?: 'HomeDataList', id: string, posterLandscapeImage?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, titleImage?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, homeable?: (
      { __typename: 'Live' }
      & { ' $fragmentRefs'?: { 'LiveShowFragmentFragment': LiveShowFragmentFragment } }
    ) | (
      { __typename: 'Movie' }
      & { ' $fragmentRefs'?: { 'MovieFragmentFragment': MovieFragmentFragment } }
    ) | (
      { __typename: 'TVShow' }
      & { ' $fragmentRefs'?: { 'TvShowFragmentFragment': TvShowFragmentFragment } }
    ) | null } | null> | null } & { ' $fragmentName'?: 'HomeDataFragmentFragment' };

export type LiveShowFragmentFragment = { __typename?: 'Live', id: string, title: string, slug: string, cdn_playback_id?: string | null, cdn_content_status?: string | null, is_rented?: boolean | null, is_free: boolean, for_subscriber: boolean, price: number, description_en?: string | null, description_mz?: string | null, imageLandscape?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imagePortrait?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null } & { ' $fragmentName'?: 'LiveShowFragmentFragment' };

export type MovieFragmentFragment = { __typename?: 'Movie', id: string, title: string, description_en?: string | null, description_mz?: string | null, production_year?: string | null, age_label?: string | null, duration: number, genre?: string | null, slug: string, imageLandscape?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imagePortrait?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imageSquare?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imageSquare2x?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imagePortrait2x?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null } & { ' $fragmentName'?: 'MovieFragmentFragment' };

export type TvShowFragmentFragment = { __typename?: 'TVShow', id: string, title: string, slug: string, description_en?: string | null, description_mz?: string | null, production_year?: string | null, genre?: string | null, age_label?: string | null, imageLandscape?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imagePortrait?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imageSquare?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imagePortrait2x?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imageSquare2x?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null } & { ' $fragmentName'?: 'TvShowFragmentFragment' };

export type AddWatchListMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  contentType?: InputMaybe<WatchListableType>;
}>;


export type AddWatchListMutation = { __typename?: 'Mutation', addWatchList?: { __typename?: 'WatchList', id: string } | null };

export type DeleteMyWatchListMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteMyWatchListMutation = { __typename?: 'Mutation', deleteMyWatchList?: { __typename?: 'WatchList', id: string } | null };

export type GenerateContentUrlMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  contentType: WatchableContentTypes;
}>;


export type GenerateContentUrlMutation = { __typename?: 'Mutation', generateContentUrl?: { __typename?: 'GenerateUrlResponse', url: string, story_board_url?: string | null } | null };

export type RentLiveMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  redirectURL?: InputMaybe<Scalars['String']['input']>;
}>;


export type RentLiveMutation = { __typename?: 'Mutation', rentLive?: { __typename: 'PhonePePaymentResponse', goto_url: string } | { __typename: 'RazorPayPaymentResponse', order_id: string } | null };

export type RentMovieMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  redirectURL?: InputMaybe<Scalars['String']['input']>;
}>;


export type RentMovieMutation = { __typename?: 'Mutation', rentMovie?: { __typename: 'PhonePePaymentResponse', goto_url: string } | { __typename: 'RazorPayPaymentResponse', order_id: string } | null };

export type RentMultipleEpisodesMutationVariables = Exact<{
  ids: Array<Scalars['ID']['input']> | Scalars['ID']['input'];
  totalAmount: Scalars['Float']['input'];
}>;


export type RentMultipleEpisodesMutation = { __typename?: 'Mutation', rentMultipleEpisodes?: { __typename: 'PhonePePaymentResponse', goto_url: string } | { __typename: 'RazorPayPaymentResponse', order_id: string } | null };

export type SubscribeToPlanMutationVariables = Exact<{
  planId: Scalars['ID']['input'];
  redirectUrl?: InputMaybe<Scalars['String']['input']>;
}>;


export type SubscribeToPlanMutation = { __typename?: 'Mutation', subscribeToPlan?: { __typename: 'PhonePePaymentResponse', goto_url: string } | { __typename: 'RazorPayPaymentResponse', order_id: string, price: number } | null };

export type UpsertWatchedDurationMutationVariables = Exact<{
  contentId: Scalars['ID']['input'];
  contentType: WatchDurationContentType;
  duration: Scalars['Int']['input'];
  totalDuration: Scalars['Int']['input'];
}>;


export type UpsertWatchedDurationMutation = { __typename?: 'Mutation', upsertWatchedDuration?: { __typename?: 'SuccessResponse', message: string } | null };

export type UserLoginMutationVariables = Exact<{
  mobileNumber?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
}>;


export type UserLoginMutation = { __typename?: 'Mutation', userLogin?: { __typename?: 'LoginOTPResponse', otp_id?: number | null } | null };

export type VerifyOtpMutationVariables = Exact<{
  otpId: Scalars['Int']['input'];
  code: Scalars['Int']['input'];
}>;


export type VerifyOtpMutation = { __typename?: 'Mutation', userLoginVerifyOTP?: { __typename?: 'UserResponse', token?: string | null, user?: { __typename?: 'User', id: string } | null } | null };

export type GetPaymentStatusQueryVariables = Exact<{
  orderID: Scalars['String']['input'];
}>;


export type GetPaymentStatusQuery = { __typename?: 'Query', getPaymentStatus?: { __typename?: 'PaymentStatusResponse', status?: string | null } | null };

export type GetMeQueryVariables = Exact<{ [key: string]: never; }>;


export type GetMeQuery = { __typename?: 'Query', getMe?: { __typename?: 'User', id: string, username?: string | null, mobile_number?: string | null, language: string, email?: string | null, subscription?: { __typename?: 'UserSubscription', id: string, user_id: string, plan_id: string, start_date?: any | null, end_date?: any | null, subscription_status?: string | null, plan?: { __typename?: 'SubscriptionPlan', id: string, name: string, price: number, duration: number } | null } | null } | null };

export type MovieByIdQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type MovieByIdQuery = { __typename?: 'Query', movieById?: (
    { __typename?: 'Movie', id: string, title: string, slug: string, subtitle_path_en?: string | null, subtitle_path_mz?: string | null, description_en?: string | null, description_mz?: string | null, duration: number, age_label?: string | null, casts?: string | null, production_year?: string | null, genre?: string | null, price: number, is_rented?: boolean | null, cdn_playback_id?: string | null, for_subscriber?: boolean | null, is_free: boolean, isAddedToWatchList: number, imagePortrait?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imageLandscape?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, trailer?: { __typename?: 'ContentTrailer', cdn_playback_id?: string | null } | null, continueWatching?: { __typename?: 'WatchHistory', watched_duration: number } | null }
    & { ' $fragmentRefs'?: { 'MovieActionFragment': MovieActionFragment } }
  ) | null };

export type MovieBySlugQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type MovieBySlugQuery = { __typename?: 'Query', getMovieBySlug?: (
    { __typename?: 'Movie', id: string, title: string, subtitle_path_en?: string | null, subtitle_path_mz?: string | null, description_en?: string | null, description_mz?: string | null, duration: number, age_label?: string | null, casts?: string | null, production_year?: string | null, genre?: string | null, price: number, is_rented?: boolean | null, cdn_playback_id?: string | null, for_subscriber?: boolean | null, is_free: boolean, isAddedToWatchList: number, imagePortrait?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imageLandscape?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, trailer?: { __typename?: 'ContentTrailer', cdn_playback_id?: string | null } | null, continueWatching?: { __typename?: 'WatchHistory', watched_duration: number } | null }
    & { ' $fragmentRefs'?: { 'MovieActionFragment': MovieActionFragment } }
  ) | null };

export type SeasonByIdQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type SeasonByIdQuery = { __typename?: 'Query', tvShowSeasonById?: (
    { __typename?: 'TVShowSeason', id: string, title: string, isAddedToWatchList: number, trailer?: { __typename?: 'ContentTrailer', cdn_playback_id?: string | null } | null, episodes?: Array<(
      { __typename: 'TVShowEpisodes' }
      & { ' $fragmentRefs'?: { 'TvEpisodeFragmentFragment': TvEpisodeFragmentFragment } }
    ) | null> | null }
    & { ' $fragmentRefs'?: { 'ShowActionFragment': ShowActionFragment } }
  ) | null };

export type ShowByIdQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type ShowByIdQuery = { __typename?: 'Query', tvShowById?: { __typename?: 'TVShow', id: string, title: string, description_en?: string | null, description_mz?: string | null, age_label?: string | null, production_year?: string | null, genre?: string | null, casts?: string | null, price_per_episode: number, imageLandscape?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, seasons?: Array<{ __typename?: 'TVShowSeason', id: string, title: string, episodes?: Array<{ __typename?: 'TVShowEpisodes', id: string, cdn_playback_id?: string | null } | null> | null } | null> | null } | null };

export type TvShowBySlugQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type TvShowBySlugQuery = { __typename?: 'Query', getTvShowBySlug?: { __typename?: 'TVShow', id: string, title: string, description_en?: string | null, description_mz?: string | null, age_label?: string | null, production_year?: string | null, genre?: string | null, casts?: string | null, price_per_episode: number, imageLandscape?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, seasons?: Array<{ __typename?: 'TVShowSeason', id: string, title: string, episodes?: Array<{ __typename?: 'TVShowEpisodes', id: string, cdn_playback_id?: string | null } | null> | null } | null> | null } | null };

export type SubscriptionPlansQueryVariables = Exact<{ [key: string]: never; }>;


export type SubscriptionPlansQuery = { __typename?: 'Query', subscriptionPlans?: Array<{ __typename?: 'SubscriptionPlan', id: string, name: string, price: number, duration: number } | null> | null };

export type HomeDataQueryVariables = Exact<{ [key: string]: never; }>;


export type HomeDataQuery = { __typename?: 'Query', getHomeData: { __typename?: 'GetHomeDataPaginator', data?: Array<(
      { __typename?: 'HomeData', id: string, category: string, order: number }
      & { ' $fragmentRefs'?: { 'HomeDataFragmentFragment': HomeDataFragmentFragment } }
    )> | null }, getContinueWatchingList?: Array<(
    { __typename?: 'WatchHistory' }
    & { ' $fragmentRefs'?: { 'ContinueWatchingFragmentFragment': ContinueWatchingFragmentFragment } }
  )> | null };

export type MovieActionFragment = { __typename?: 'Movie', id: string, for_subscriber?: boolean | null, is_rented?: boolean | null, is_free: boolean, price: number, continueWatching?: { __typename?: 'WatchHistory', watched_duration: number } | null } & { ' $fragmentName'?: 'MovieActionFragment' };

export type MovieBySlugServerQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type MovieBySlugServerQuery = { __typename?: 'Query', getMovieBySlug?: { __typename?: 'Movie', id: string, title: string, description_en?: string | null, imageLandscape?: { __typename?: 'AppImage', path?: string | null } | null } | null };

export type MyWatchListQueryVariables = Exact<{
  page?: InputMaybe<Scalars['Int']['input']>;
  first: Scalars['Int']['input'];
}>;


export type MyWatchListQuery = { __typename?: 'Query', myWatchList: { __typename?: 'WatchListPaginator', data: Array<{ __typename?: 'WatchList', watch_listable?: (
        { __typename: 'Movie' }
        & { ' $fragmentRefs'?: { 'WatchListMovieFragmentFragment': WatchListMovieFragmentFragment } }
      ) | { __typename: 'TVShow' } | (
        { __typename: 'TVShowEpisodes' }
        & { ' $fragmentRefs'?: { 'WatchListEpisodeFragmentFragment': WatchListEpisodeFragmentFragment } }
      ) | (
        { __typename: 'TVShowSeason' }
        & { ' $fragmentRefs'?: { 'WatchListSeasonFragmentFragment': WatchListSeasonFragmentFragment } }
      ) | null }>, paginatorInfo: { __typename?: 'PaginatorInfo', lastPage: number, total: number } } };

export type WatchListEpisodeFragmentFragment = { __typename?: 'TVShowEpisodes', id: string, title: string, description_en?: string | null, description_mz?: string | null, sub_header: string, tv_show_id: string, tv_show_season_id: string, imageLandscape?: { __typename?: 'AppImage', id: string, hash?: string | null, path?: string | null } | null, season?: { __typename?: 'TVShowSeason', title: string } | null, tvShow?: { __typename?: 'TVShow', slug: string, title: string } | null } & { ' $fragmentName'?: 'WatchListEpisodeFragmentFragment' };

export type WatchListMovieFragmentFragment = { __typename?: 'Movie', id: string, slug: string, title: string, description_en?: string | null, description_mz?: string | null, imageLandscape?: { __typename?: 'AppImage', id: string, hash?: string | null, path?: string | null } | null } & { ' $fragmentName'?: 'WatchListMovieFragmentFragment' };

export type WatchListSeasonFragmentFragment = { __typename?: 'TVShowSeason', id: string, title: string, description_mz?: string | null, description_en?: string | null, tvShow?: { __typename?: 'TVShow', slug: string, id: string, title: string, imageLandscape?: { __typename?: 'AppImage', id: string, hash?: string | null, path?: string | null } | null } | null } & { ' $fragmentName'?: 'WatchListSeasonFragmentFragment' };

export type ShowActionFragment = { __typename?: 'TVShowSeason', id: string, rentable_status?: string | null, is_rented?: boolean | null, title: string, episodes?: Array<(
    { __typename?: 'TVShowEpisodes', id: string }
    & { ' $fragmentRefs'?: { 'TvEpisodeFragmentFragment': TvEpisodeFragmentFragment } }
  ) | null> | null } & { ' $fragmentName'?: 'ShowActionFragment' };

export type DeleteAccountMutationVariables = Exact<{ [key: string]: never; }>;


export type DeleteAccountMutation = { __typename?: 'Mutation', deleteMyAccount?: { __typename?: 'SuccessResponse', message: string } | null };

export type RentListEpisodeFragmentFragment = { __typename?: 'TVShowEpisodes', id: string, title: string, season?: { __typename?: 'TVShowSeason', title: string } | null, tvShow?: { __typename?: 'TVShow', title: string } | null } & { ' $fragmentName'?: 'RentListEpisodeFragmentFragment' };

export type RentListLiveFragmentFragment = { __typename?: 'Live', id: string, title: string } & { ' $fragmentName'?: 'RentListLiveFragmentFragment' };

export type RentListMovieFragmentFragment = { __typename?: 'Movie', id: string, title: string } & { ' $fragmentName'?: 'RentListMovieFragmentFragment' };

export type RentListSeasonFragmentFragment = { __typename?: 'TVShowSeason', id: string, title: string, tvShow?: { __typename?: 'TVShow', title: string } | null } & { ' $fragmentName'?: 'RentListSeasonFragmentFragment' };

export type MyContentRentListQueryVariables = Exact<{
  page?: InputMaybe<Scalars['Int']['input']>;
  first: Scalars['Int']['input'];
}>;


export type MyContentRentListQuery = { __typename?: 'Query', myContentRentList: { __typename?: 'UserRentContentPaginator', data: Array<{ __typename?: 'UserRentContent', id: string, status: string, price: number, start_date?: any | null, end_date?: any | null, rentable?: (
        { __typename: 'Live' }
        & { ' $fragmentRefs'?: { 'RentListLiveFragmentFragment': RentListLiveFragmentFragment } }
      ) | (
        { __typename: 'Movie' }
        & { ' $fragmentRefs'?: { 'RentListMovieFragmentFragment': RentListMovieFragmentFragment } }
      ) | (
        { __typename: 'TVShowEpisodes' }
        & { ' $fragmentRefs'?: { 'RentListEpisodeFragmentFragment': RentListEpisodeFragmentFragment } }
      ) | (
        { __typename: 'TVShowSeason' }
        & { ' $fragmentRefs'?: { 'RentListSeasonFragmentFragment': RentListSeasonFragmentFragment } }
      ) | null }>, paginatorInfo: { __typename?: 'PaginatorInfo', lastPage: number, total: number } } };

export type SearchItemLiveFragmentFragment = { __typename?: 'Live', id: string, title: string, slug: string, cdn_playback_id?: string | null, is_rented?: boolean | null, is_free: boolean, price: number, for_subscriber: boolean, imageLandscape?: { __typename?: 'AppImage', path?: string | null } | null } & { ' $fragmentName'?: 'SearchItemLiveFragmentFragment' };

export type SearchItemMovieFragmentFragment = { __typename?: 'Movie', id: string, title: string, slug: string, imageLandscape?: { __typename?: 'AppImage', path?: string | null, hash?: string | null } | null } & { ' $fragmentName'?: 'SearchItemMovieFragmentFragment' };

export type SearchItemShowFragmentFragment = { __typename?: 'TVShow', id: string, title: string, slug: string, imageLandscape?: { __typename?: 'AppImage', path?: string | null } | null } & { ' $fragmentName'?: 'SearchItemShowFragmentFragment' };

export type SearchContentQueryVariables = Exact<{
  keyword: Scalars['String']['input'];
}>;


export type SearchContentQuery = { __typename?: 'Query', searchContent?: Array<{ __typename?: 'SearchableContent', id: string, canSearch?: (
      { __typename: 'Live', id: string, title: string, cdn_playback_id?: string | null, is_rented?: boolean | null, is_free: boolean, price: number, imageLandscape?: { __typename?: 'AppImage', path?: string | null } | null }
      & { ' $fragmentRefs'?: { 'SearchItemLiveFragmentFragment': SearchItemLiveFragmentFragment } }
    ) | (
      { __typename: 'Movie', id: string, title: string, imageLandscape?: { __typename?: 'AppImage', path?: string | null } | null }
      & { ' $fragmentRefs'?: { 'SearchItemMovieFragmentFragment': SearchItemMovieFragmentFragment } }
    ) | (
      { __typename: 'TVShow', id: string, title: string, imageLandscape?: { __typename?: 'AppImage', path?: string | null } | null }
      & { ' $fragmentRefs'?: { 'SearchItemShowFragmentFragment': SearchItemShowFragmentFragment } }
    ) | null } | null> | null };

export type UserLogoutMutationVariables = Exact<{ [key: string]: never; }>;


export type UserLogoutMutation = { __typename?: 'Mutation', userLogout?: { __typename?: 'SuccessResponse', message: string } | null };

export type UpdateMyInfoMutationVariables = Exact<{
  language?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdateMyInfoMutation = { __typename?: 'Mutation', updateMyInfo?: { __typename?: 'User', id: string, language: string } | null };

export type LiveBySlugQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type LiveBySlugQuery = { __typename?: 'Query', getLiveBySlug?: { __typename?: 'Live', id: string, title: string, cdn_playback_id?: string | null, cdn_content_status?: string | null, description_en?: string | null, imageLandscape?: { __typename?: 'AppImage', path?: string | null } | null } | null };

export type EpisodePlaylistFragmentFragment = { __typename?: 'TVShowEpisodes', id: string, title: string, cdn_playback_id?: string | null, description_en?: string | null, description_mz?: string | null, is_rented?: boolean | null, is_free: boolean, for_subscriber: boolean, continueWatching?: { __typename?: 'WatchHistory', watched_duration: number } | null, imageLandscape?: { __typename?: 'AppImage', id: string, hash?: string | null, path?: string | null } | null } & { ' $fragmentName'?: 'EpisodePlaylistFragmentFragment' };

export type TvEpisodeByIdQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type TvEpisodeByIdQuery = { __typename?: 'Query', tvShowEpisodeById?: { __typename?: 'TVShowEpisodes', id: string, title: string, tv_show_id: string, cdn_playback_id?: string | null, description_en?: string | null, continueWatching?: { __typename?: 'WatchHistory', watched_duration: number } | null, imageLandscape?: { __typename?: 'AppImage', path?: string | null } | null, season?: { __typename?: 'TVShowSeason', title: string, episodes?: Array<(
        { __typename?: 'TVShowEpisodes', cdn_playback_id?: string | null }
        & { ' $fragmentRefs'?: { 'EpisodePlaylistFragmentFragment': EpisodePlaylistFragmentFragment } }
      ) | null> | null } | null, tvShow?: { __typename?: 'TVShow', title: string, price_per_episode: number } | null } | null };

export type MoviesQueryVariables = Exact<{
  first?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  keyword?: InputMaybe<Scalars['String']['input']>;
}>;


export type MoviesQuery = { __typename?: 'Query', getMovies?: { __typename?: 'GetMoviesPaginator', data?: Array<{ __typename?: 'Movie', id: string, title: string, description_en?: string | null, description_mz?: string | null, age_label?: string | null, duration: number, production_year?: string | null, is_rented?: boolean | null, for_subscriber?: boolean | null, price: number, is_free: boolean, imagePortrait?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imageLandscape?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null }> | null, paginatorInfo: { __typename?: 'AppPaginatorInfo', hasMorePages: boolean, total: number, currentPage: number } } | null };

export type LiveShowsQueryVariables = Exact<{
  first?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  keyword?: InputMaybe<Scalars['String']['input']>;
}>;


export type LiveShowsQuery = { __typename?: 'Query', getLives?: { __typename?: 'GetLivesPaginator', data?: Array<{ __typename?: 'Live', id: string, title: string, imagePortrait?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null, imageLandscape?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null }> | null, paginatorInfo: { __typename?: 'AppPaginatorInfo', hasMorePages: boolean, currentPage: number } } | null };

export type TvShowsQueryVariables = Exact<{
  first?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  keyword?: InputMaybe<Scalars['String']['input']>;
}>;


export type TvShowsQuery = { __typename?: 'Query', getTvShows?: { __typename?: 'GetTvShowsPaginator', data?: Array<{ __typename?: 'TVShow', id: string, title: string, imagePortrait?: { __typename?: 'AppImage', id: string, path?: string | null, hash?: string | null } | null }> | null, paginatorInfo: { __typename?: 'TvShowAppPaginatorInfo', hasMorePages: boolean, currentPage: number } } | null };

export type TvShowByIdQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type TvShowByIdQuery = { __typename?: 'Query', tvShowById?: { __typename?: 'TVShow', id: string, title: string, price_per_episode: number, description_mz?: string | null, description_en?: string | null, seasons?: Array<{ __typename?: 'TVShowSeason', id: string, title: string, episodes?: Array<{ __typename?: 'TVShowEpisodes', id: string, title: string, sub_header: string, is_free: boolean, for_subscriber: boolean, is_rented?: boolean | null } | null> | null } | null> | null } | null };

export type GetContentReportQueryVariables = Exact<{
  contentType: ReportableType;
  slug: Scalars['String']['input'];
}>;


export type GetContentReportQuery = { __typename?: 'Query', getContentReport?: { __typename?: 'ContentReportResponse', producer_share: number, stats?: Array<{ __typename?: 'StatsData', purchase_count: number, title: string, total_revenue: number }> | null } | null };

export const TvEpisodeFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"TVEpisodeFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowEpisodes"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"isAddedToWatchList"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"sub_header"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_id"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_season_id"}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}},{"kind":"Field","name":{"kind":"Name","value":"total_duration"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}}]} as unknown as DocumentNode<TvEpisodeFragmentFragment, unknown>;
export const MovieFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"MovieFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"age_label"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}},{"kind":"Field","name":{"kind":"Name","value":"genre"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}}]} as unknown as DocumentNode<MovieFragmentFragment, unknown>;
export const ContinueWatchingFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"ContinueWatchingFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"WatchHistory"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"watchable_id"}},{"kind":"Field","name":{"kind":"Name","value":"watchable_type"}},{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}},{"kind":"Field","name":{"kind":"Name","value":"total_duration"}},{"kind":"Field","name":{"kind":"Name","value":"user_id"}},{"kind":"Field","name":{"kind":"Name","value":"watchable"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"TVEpisodeFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"MovieFragment"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"TVEpisodeFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowEpisodes"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"isAddedToWatchList"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"sub_header"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_id"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_season_id"}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}},{"kind":"Field","name":{"kind":"Name","value":"total_duration"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"MovieFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"age_label"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}},{"kind":"Field","name":{"kind":"Name","value":"genre"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}}]} as unknown as DocumentNode<ContinueWatchingFragmentFragment, unknown>;
export const TvShowFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"TvShowFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShow"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"genre"}},{"kind":"Field","name":{"kind":"Name","value":"age_label"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}}]} as unknown as DocumentNode<TvShowFragmentFragment, unknown>;
export const LiveShowFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"LiveShowFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Live"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_content_status"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}}]} as unknown as DocumentNode<LiveShowFragmentFragment, unknown>;
export const HomeDataFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"HomeDataFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"HomeData"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"category"}},{"kind":"Field","name":{"kind":"Name","value":"order"}},{"kind":"Field","name":{"kind":"Name","value":"display_type"}},{"kind":"Field","name":{"kind":"Name","value":"homeDataList"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"posterLandscapeImage"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"titleImage"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"homeable"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"TvShowFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"LiveShowFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"MovieFragment"}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"TvShowFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShow"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"genre"}},{"kind":"Field","name":{"kind":"Name","value":"age_label"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"LiveShowFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Live"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_content_status"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"MovieFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"age_label"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}},{"kind":"Field","name":{"kind":"Name","value":"genre"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}}]} as unknown as DocumentNode<HomeDataFragmentFragment, unknown>;
export const MovieActionFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"MovieAction"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}}]}}]}}]} as unknown as DocumentNode<MovieActionFragment, unknown>;
export const WatchListEpisodeFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"WatchListEpisodeFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowEpisodes"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"sub_header"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"path"}}]}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_id"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_season_id"}},{"kind":"Field","name":{"kind":"Name","value":"season"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"tvShow"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}}]} as unknown as DocumentNode<WatchListEpisodeFragmentFragment, unknown>;
export const WatchListMovieFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"WatchListMovieFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]} as unknown as DocumentNode<WatchListMovieFragmentFragment, unknown>;
export const WatchListSeasonFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"WatchListSeasonFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowSeason"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"tvShow"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]}}]} as unknown as DocumentNode<WatchListSeasonFragmentFragment, unknown>;
export const ShowActionFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"ShowAction"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowSeason"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"rentable_status"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"episodes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"TVEpisodeFragment"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"TVEpisodeFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowEpisodes"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"isAddedToWatchList"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"sub_header"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_id"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_season_id"}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}},{"kind":"Field","name":{"kind":"Name","value":"total_duration"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}}]} as unknown as DocumentNode<ShowActionFragment, unknown>;
export const RentListEpisodeFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"RentListEpisodeFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowEpisodes"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"season"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"tvShow"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}}]} as unknown as DocumentNode<RentListEpisodeFragmentFragment, unknown>;
export const RentListLiveFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"RentListLiveFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Live"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]} as unknown as DocumentNode<RentListLiveFragmentFragment, unknown>;
export const RentListMovieFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"RentListMovieFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]} as unknown as DocumentNode<RentListMovieFragmentFragment, unknown>;
export const RentListSeasonFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"RentListSeasonFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowSeason"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"tvShow"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}}]} as unknown as DocumentNode<RentListSeasonFragmentFragment, unknown>;
export const SearchItemLiveFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SearchItemLiveFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Live"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]} as unknown as DocumentNode<SearchItemLiveFragmentFragment, unknown>;
export const SearchItemMovieFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SearchItemMovieFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}}]} as unknown as DocumentNode<SearchItemMovieFragmentFragment, unknown>;
export const SearchItemShowFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SearchItemShowFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShow"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]} as unknown as DocumentNode<SearchItemShowFragmentFragment, unknown>;
export const EpisodePlaylistFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"EpisodePlaylistFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowEpisodes"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]} as unknown as DocumentNode<EpisodePlaylistFragmentFragment, unknown>;
export const RentEpisodeDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RentEpisode"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"redirectURL"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"rentTVShowEpisode"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"redirect_url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"redirectURL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"PhonePePaymentResponse"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"goto_url"}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"RazorPayPaymentResponse"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}}]}}]}}]}}]} as unknown as DocumentNode<RentEpisodeMutation, RentEpisodeMutationVariables>;
export const AddWatchListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddWatchList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"contentType"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"WatchListableType"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"addWatchList"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"content_type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"contentType"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<AddWatchListMutation, AddWatchListMutationVariables>;
export const DeleteMyWatchListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteMyWatchList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteMyWatchList"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<DeleteMyWatchListMutation, DeleteMyWatchListMutationVariables>;
export const GenerateContentUrlDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"GenerateContentUrl"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"contentType"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"WatchableContentTypes"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"generateContentUrl"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"content_type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"contentType"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"story_board_url"}}]}}]}}]} as unknown as DocumentNode<GenerateContentUrlMutation, GenerateContentUrlMutationVariables>;
export const RentLiveDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RentLive"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"redirectURL"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"rentLive"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"redirect_url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"redirectURL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"PhonePePaymentResponse"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"goto_url"}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"RazorPayPaymentResponse"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}}]}}]}}]}}]} as unknown as DocumentNode<RentLiveMutation, RentLiveMutationVariables>;
export const RentMovieDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RentMovie"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"redirectURL"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"rentMovie"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"redirect_url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"redirectURL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"PhonePePaymentResponse"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"goto_url"}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"RazorPayPaymentResponse"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}}]}}]}}]}}]} as unknown as DocumentNode<RentMovieMutation, RentMovieMutationVariables>;
export const RentMultipleEpisodesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RentMultipleEpisodes"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"ids"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"totalAmount"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"rentMultipleEpisodes"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"ids"},"value":{"kind":"Variable","name":{"kind":"Name","value":"ids"}}},{"kind":"Argument","name":{"kind":"Name","value":"total_amount"},"value":{"kind":"Variable","name":{"kind":"Name","value":"totalAmount"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"PhonePePaymentResponse"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"goto_url"}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"RazorPayPaymentResponse"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}}]}}]}}]}}]} as unknown as DocumentNode<RentMultipleEpisodesMutation, RentMultipleEpisodesMutationVariables>;
export const SubscribeToPlanDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"SubscribeToPlan"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"planId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"redirectUrl"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"subscribeToPlan"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"plan_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"planId"}}},{"kind":"Argument","name":{"kind":"Name","value":"redirect_url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"redirectUrl"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"PhonePePaymentResponse"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"goto_url"}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"RazorPayPaymentResponse"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}},{"kind":"Field","name":{"kind":"Name","value":"price"}}]}}]}}]}}]} as unknown as DocumentNode<SubscribeToPlanMutation, SubscribeToPlanMutationVariables>;
export const UpsertWatchedDurationDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpsertWatchedDuration"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"contentId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"contentType"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"WatchDurationContentType"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"duration"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"totalDuration"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"upsertWatchedDuration"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"content_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"contentId"}}},{"kind":"Argument","name":{"kind":"Name","value":"content_type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"contentType"}}},{"kind":"Argument","name":{"kind":"Name","value":"duration"},"value":{"kind":"Variable","name":{"kind":"Name","value":"duration"}}},{"kind":"Argument","name":{"kind":"Name","value":"total_duration"},"value":{"kind":"Variable","name":{"kind":"Name","value":"totalDuration"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UpsertWatchedDurationMutation, UpsertWatchedDurationMutationVariables>;
export const UserLoginDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UserLogin"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"email"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"userLogin"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"mobile_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}}},{"kind":"Argument","name":{"kind":"Name","value":"email"},"value":{"kind":"Variable","name":{"kind":"Name","value":"email"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"otp_id"}}]}}]}}]} as unknown as DocumentNode<UserLoginMutation, UserLoginMutationVariables>;
export const VerifyOtpDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"VerifyOtp"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"otpId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"code"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"userLoginVerifyOTP"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"otp_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"otpId"}}},{"kind":"Argument","name":{"kind":"Name","value":"code"},"value":{"kind":"Variable","name":{"kind":"Name","value":"code"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"token"}},{"kind":"Field","name":{"kind":"Name","value":"user"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]}}]} as unknown as DocumentNode<VerifyOtpMutation, VerifyOtpMutationVariables>;
export const GetPaymentStatusDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetPaymentStatus"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"orderID"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getPaymentStatus"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"order_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orderID"}}},{"kind":"Argument","name":{"kind":"Name","value":"provider"},"value":{"kind":"EnumValue","value":"RZPAY"}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"}}]}}]}}]} as unknown as DocumentNode<GetPaymentStatusQuery, GetPaymentStatusQueryVariables>;
export const GetMeDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetMe"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getMe"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"username"}},{"kind":"Field","name":{"kind":"Name","value":"mobile_number"}},{"kind":"Field","name":{"kind":"Name","value":"language"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"subscription"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"user_id"}},{"kind":"Field","name":{"kind":"Name","value":"plan_id"}},{"kind":"Field","name":{"kind":"Name","value":"start_date"}},{"kind":"Field","name":{"kind":"Name","value":"end_date"}},{"kind":"Field","name":{"kind":"Name","value":"subscription_status"}},{"kind":"Field","name":{"kind":"Name","value":"plan"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetMeQuery, GetMeQueryVariables>;
export const MovieByIdDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"MovieById"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"movieById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"subtitle_path_en"}},{"kind":"Field","name":{"kind":"Name","value":"subtitle_path_mz"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}},{"kind":"Field","name":{"kind":"Name","value":"age_label"}},{"kind":"Field","name":{"kind":"Name","value":"casts"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"genre"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"isAddedToWatchList"}},{"kind":"Field","name":{"kind":"Name","value":"trailer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}}]}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}}]}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"MovieAction"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"MovieAction"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}}]}}]}}]} as unknown as DocumentNode<MovieByIdQuery, MovieByIdQueryVariables>;
export const MovieBySlugDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"MovieBySlug"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"slug"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getMovieBySlug"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"slug"},"value":{"kind":"Variable","name":{"kind":"Name","value":"slug"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"subtitle_path_en"}},{"kind":"Field","name":{"kind":"Name","value":"subtitle_path_mz"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}},{"kind":"Field","name":{"kind":"Name","value":"age_label"}},{"kind":"Field","name":{"kind":"Name","value":"casts"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"genre"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"isAddedToWatchList"}},{"kind":"Field","name":{"kind":"Name","value":"trailer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}}]}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}}]}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"MovieAction"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"MovieAction"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}}]}}]}}]} as unknown as DocumentNode<MovieBySlugQuery, MovieBySlugQueryVariables>;
export const SeasonByIdDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"SeasonById"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"tvShowSeasonById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"trailer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}}]}},{"kind":"Field","name":{"kind":"Name","value":"episodes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"TVEpisodeFragment"}}]}},{"kind":"Field","name":{"kind":"Name","value":"isAddedToWatchList"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"ShowAction"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"TVEpisodeFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowEpisodes"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"isAddedToWatchList"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"sub_header"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_id"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_season_id"}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}},{"kind":"Field","name":{"kind":"Name","value":"total_duration"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"ShowAction"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowSeason"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"rentable_status"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"episodes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"TVEpisodeFragment"}}]}}]}}]} as unknown as DocumentNode<SeasonByIdQuery, SeasonByIdQueryVariables>;
export const ShowByIdDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"ShowByID"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"tvShowById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"age_label"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"genre"}},{"kind":"Field","name":{"kind":"Name","value":"casts"}},{"kind":"Field","name":{"kind":"Name","value":"price_per_episode"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"seasons"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"episodes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}}]}}]}}]}}]}}]} as unknown as DocumentNode<ShowByIdQuery, ShowByIdQueryVariables>;
export const TvShowBySlugDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"TvShowBySlug"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"slug"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getTvShowBySlug"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"slug"},"value":{"kind":"Variable","name":{"kind":"Name","value":"slug"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"age_label"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"genre"}},{"kind":"Field","name":{"kind":"Name","value":"casts"}},{"kind":"Field","name":{"kind":"Name","value":"price_per_episode"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"seasons"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"episodes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}}]}}]}}]}}]}}]} as unknown as DocumentNode<TvShowBySlugQuery, TvShowBySlugQueryVariables>;
export const SubscriptionPlansDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"SubscriptionPlans"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"subscriptionPlans"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}}]}}]}}]} as unknown as DocumentNode<SubscriptionPlansQuery, SubscriptionPlansQueryVariables>;
export const HomeDataDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"HomeData"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getHomeData"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"IntValue","value":"20"}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"category"}},{"kind":"Field","name":{"kind":"Name","value":"order"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"HomeDataFragment"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"getContinueWatchingList"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"ContinueWatchingFragment"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"TvShowFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShow"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"genre"}},{"kind":"Field","name":{"kind":"Name","value":"age_label"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"LiveShowFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Live"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_content_status"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"MovieFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"age_label"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}},{"kind":"Field","name":{"kind":"Name","value":"genre"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageSquare2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait2x"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"TVEpisodeFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowEpisodes"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"isAddedToWatchList"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"sub_header"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_id"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_season_id"}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}},{"kind":"Field","name":{"kind":"Name","value":"total_duration"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"HomeDataFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"HomeData"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"category"}},{"kind":"Field","name":{"kind":"Name","value":"order"}},{"kind":"Field","name":{"kind":"Name","value":"display_type"}},{"kind":"Field","name":{"kind":"Name","value":"homeDataList"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"posterLandscapeImage"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"titleImage"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"homeable"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"TvShowFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"LiveShowFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"MovieFragment"}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"ContinueWatchingFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"WatchHistory"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"watchable_id"}},{"kind":"Field","name":{"kind":"Name","value":"watchable_type"}},{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}},{"kind":"Field","name":{"kind":"Name","value":"total_duration"}},{"kind":"Field","name":{"kind":"Name","value":"user_id"}},{"kind":"Field","name":{"kind":"Name","value":"watchable"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"TVEpisodeFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"MovieFragment"}}]}}]}}]} as unknown as DocumentNode<HomeDataQuery, HomeDataQueryVariables>;
export const MovieBySlugServerDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"MovieBySlugServer"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"slug"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getMovieBySlug"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"slug"},"value":{"kind":"Variable","name":{"kind":"Name","value":"slug"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]}}]} as unknown as DocumentNode<MovieBySlugServerQuery, MovieBySlugServerQueryVariables>;
export const MyWatchListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"MyWatchList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"myWatchList"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watch_listable"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"WatchListMovieFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"WatchListSeasonFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"WatchListEpisodeFragment"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"lastPage"}},{"kind":"Field","name":{"kind":"Name","value":"total"}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"WatchListMovieFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"WatchListSeasonFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowSeason"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"tvShow"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"WatchListEpisodeFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowEpisodes"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"sub_header"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"path"}}]}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_id"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_season_id"}},{"kind":"Field","name":{"kind":"Name","value":"season"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"tvShow"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}}]} as unknown as DocumentNode<MyWatchListQuery, MyWatchListQueryVariables>;
export const DeleteAccountDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteAccount"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteMyAccount"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<DeleteAccountMutation, DeleteAccountMutationVariables>;
export const MyContentRentListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"MyContentRentList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"myContentRentList"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"start_date"}},{"kind":"Field","name":{"kind":"Name","value":"end_date"}},{"kind":"Field","name":{"kind":"Name","value":"rentable"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"RentListEpisodeFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"RentListLiveFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"RentListMovieFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"RentListSeasonFragment"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"lastPage"}},{"kind":"Field","name":{"kind":"Name","value":"total"}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"RentListEpisodeFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowEpisodes"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"season"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"tvShow"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"RentListLiveFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Live"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"RentListMovieFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"RentListSeasonFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowSeason"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"tvShow"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}}]} as unknown as DocumentNode<MyContentRentListQuery, MyContentRentListQueryVariables>;
export const SearchContentDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"SearchContent"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"searchContent"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"keyword"},"value":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"canSearch"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"SearchItemMovieFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"SearchItemShowFragment"}},{"kind":"FragmentSpread","name":{"kind":"Name","value":"SearchItemLiveFragment"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShow"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Live"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SearchItemMovieFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Movie"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SearchItemShowFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShow"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SearchItemLiveFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Live"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]} as unknown as DocumentNode<SearchContentQuery, SearchContentQueryVariables>;
export const UserLogoutDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UserLogout"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"userLogout"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UserLogoutMutation, UserLogoutMutationVariables>;
export const UpdateMyInfoDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateMyInfo"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"language"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateMyInfo"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"language"},"value":{"kind":"Variable","name":{"kind":"Name","value":"language"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"language"}}]}}]}}]} as unknown as DocumentNode<UpdateMyInfoMutation, UpdateMyInfoMutationVariables>;
export const LiveBySlugDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"LiveBySlug"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"slug"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getLiveBySlug"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"slug"},"value":{"kind":"Variable","name":{"kind":"Name","value":"slug"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_content_status"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]}}]} as unknown as DocumentNode<LiveBySlugQuery, LiveBySlugQueryVariables>;
export const TvEpisodeByIdDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"TvEpisodeById"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"tvShowEpisodeById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"tv_show_id"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}}]}},{"kind":"Field","name":{"kind":"Name","value":"season"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"episodes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"EpisodePlaylistFragment"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"tvShow"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"price_per_episode"}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"EpisodePlaylistFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TVShowEpisodes"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"cdn_playback_id"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"continueWatching"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"watched_duration"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]} as unknown as DocumentNode<TvEpisodeByIdQuery, TvEpisodeByIdQueryVariables>;
export const MoviesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"Movies"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getMovies"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"keyword"},"value":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"age_label"}},{"kind":"Field","name":{"kind":"Name","value":"duration"}},{"kind":"Field","name":{"kind":"Name","value":"production_year"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hasMorePages"}},{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"currentPage"}}]}}]}}]}}]} as unknown as DocumentNode<MoviesQuery, MoviesQueryVariables>;
export const LiveShowsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"LiveShows"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getLives"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"keyword"},"value":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"imageLandscape"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hasMorePages"}},{"kind":"Field","name":{"kind":"Name","value":"currentPage"}}]}}]}}]}}]} as unknown as DocumentNode<LiveShowsQuery, LiveShowsQueryVariables>;
export const TvShowsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"TvShows"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getTvShows"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"keyword"},"value":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"imagePortrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hasMorePages"}},{"kind":"Field","name":{"kind":"Name","value":"currentPage"}}]}}]}}]}}]} as unknown as DocumentNode<TvShowsQuery, TvShowsQueryVariables>;
export const TvShowByIdDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"TvShowById"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"tvShowById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"price_per_episode"}},{"kind":"Field","name":{"kind":"Name","value":"description_mz"}},{"kind":"Field","name":{"kind":"Name","value":"description_en"}},{"kind":"Field","name":{"kind":"Name","value":"seasons"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"episodes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"sub_header"}},{"kind":"Field","name":{"kind":"Name","value":"is_free"}},{"kind":"Field","name":{"kind":"Name","value":"for_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"is_rented"}}]}}]}}]}}]}}]} as unknown as DocumentNode<TvShowByIdQuery, TvShowByIdQueryVariables>;
export const GetContentReportDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetContentReport"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"contentType"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ReportableType"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"slug"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getContentReport"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"slug"},"value":{"kind":"Variable","name":{"kind":"Name","value":"slug"}}},{"kind":"Argument","name":{"kind":"Name","value":"content_type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"contentType"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"producer_share"}},{"kind":"Field","name":{"kind":"Name","value":"stats"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"purchase_count"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"total_revenue"}}]}}]}}]}}]} as unknown as DocumentNode<GetContentReportQuery, GetContentReportQueryVariables>;