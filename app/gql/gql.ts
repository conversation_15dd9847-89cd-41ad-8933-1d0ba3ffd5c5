/* eslint-disable */
import * as types from './graphql';
import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
const documents = {
    "\n  mutation RentEpisode($id: ID!, $redirectURL: String) {\n    rentTVShowEpisode(id: $id, redirect_url: $redirectURL) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n      }\n    }\n  }\n": types.RentEpisodeDocument,
    "\n  fragment ContinueWatchingFragment on WatchHistory {\n    id\n    watchable_id\n    watchable_type\n    watched_duration\n    total_duration\n    user_id\n    watchable {\n      __typename\n      ...TVEpisodeFragment\n      ...MovieFragment\n    }\n  }\n": types.ContinueWatchingFragmentFragmentDoc,
    "\n  fragment TVEpisodeFragment on TVShowEpisodes {\n    __typename\n    id\n    cdn_playback_id\n    isAddedToWatchList\n    title\n    sub_header\n    duration\n    is_rented\n    is_free\n    description_en\n    description_mz\n    for_subscriber\n    tv_show_id\n    tv_show_season_id\n    continueWatching {\n      watched_duration\n      total_duration\n    }\n    imageLandscape {\n      id\n      path\n      hash\n    }\n  }\n": types.TvEpisodeFragmentFragmentDoc,
    "\n  fragment HomeDataFragment on HomeData {\n    id\n    category\n    order\n    display_type\n    homeDataList {\n      id\n      posterLandscapeImage {\n        id\n        path\n        hash\n      }\n      titleImage {\n        id\n        path\n        hash\n      }\n      homeable {\n        __typename\n        ...TvShowFragment\n        ...LiveShowFragment\n        ...MovieFragment\n      }\n    }\n  }\n": types.HomeDataFragmentFragmentDoc,
    "\n  fragment LiveShowFragment on Live {\n    id\n    title\n    slug\n    cdn_playback_id\n    cdn_content_status\n    is_rented\n    is_free\n    for_subscriber\n    price\n    description_en\n    description_mz\n    imageLandscape {\n      id\n      path\n      hash\n    }\n    imagePortrait {\n      id\n      path\n      hash\n    }\n  }\n": types.LiveShowFragmentFragmentDoc,
    "\n  fragment MovieFragment on Movie {\n    id\n    title\n    description_en\n    description_mz\n    production_year\n    age_label\n    duration\n    genre\n    slug\n    imageLandscape {\n      id\n      path\n      hash\n    }\n    imagePortrait {\n      id\n      path\n      hash\n    }\n    imageSquare {\n      id\n      path\n      hash\n    }\n    imageSquare2x {\n      id\n      path\n      hash\n    }\n    imagePortrait2x {\n      id\n      path\n      hash\n    }\n  }\n": types.MovieFragmentFragmentDoc,
    "\n  fragment TvShowFragment on TVShow {\n    id\n    title\n    slug\n    description_en\n    description_mz\n    production_year\n    genre\n    age_label\n    imageLandscape {\n      id\n      path\n      hash\n    }\n    imagePortrait {\n      id\n      path\n      hash\n    }\n    imageSquare {\n      id\n      path\n      hash\n    }\n    imagePortrait2x {\n      id\n      path\n      hash\n    }\n    imageSquare2x {\n      id\n      path\n      hash\n    }\n  }\n": types.TvShowFragmentFragmentDoc,
    "\n  mutation AddWatchList($id: ID!, $contentType: WatchListableType) {\n    addWatchList(id: $id, content_type: $contentType) {\n      id\n    }\n  }\n": types.AddWatchListDocument,
    "\n  mutation DeleteMyWatchList($id: ID!) {\n    deleteMyWatchList(id: $id) {\n      id\n    }\n  }\n": types.DeleteMyWatchListDocument,
    "\n  mutation GenerateContentUrl(\n    $id: ID!\n    $contentType: WatchableContentTypes!\n  ) {\n    generateContentUrl(\n      id: $id\n      content_type: $contentType\n    ) {\n      url\n      story_board_url\n    }\n  }\n": types.GenerateContentUrlDocument,
    "\n  mutation RentLive($id: ID!, $redirectURL: String) {\n    rentLive(id: $id, redirect_url: $redirectURL) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n      }\n    }\n  }\n": types.RentLiveDocument,
    "\n  mutation RentMovie($id: ID!, $redirectURL: String) {\n    rentMovie(id: $id, redirect_url: $redirectURL) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n      }\n    }\n  }\n": types.RentMovieDocument,
    "\n  mutation RentMultipleEpisodes($ids: [ID!]!, $totalAmount: Float!) {\n    rentMultipleEpisodes(ids: $ids, total_amount: $totalAmount) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n      }\n    }\n  }\n": types.RentMultipleEpisodesDocument,
    "\n  mutation SubscribeToPlan($planId: ID!, $redirectUrl: String) {\n    subscribeToPlan(plan_id: $planId, redirect_url: $redirectUrl) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n        price\n      }\n    }\n  }\n": types.SubscribeToPlanDocument,
    "\n  mutation UpsertWatchedDuration(\n    $contentId: ID!\n    $contentType: WatchDurationContentType!\n    $duration: Int!\n    $totalDuration: Int!\n  ) {\n    upsertWatchedDuration(\n      content_id: $contentId\n      content_type: $contentType\n      duration: $duration\n      total_duration: $totalDuration\n    ) {\n      message\n    }\n  }\n": types.UpsertWatchedDurationDocument,
    "\n  mutation UserLogin($mobileNumber: String, $email: String) {\n    userLogin(mobile_number: $mobileNumber, email: $email) {\n      otp_id\n    }\n  }\n": types.UserLoginDocument,
    "\n  mutation VerifyOtp($otpId: Int!, $code: Int!) {\n    userLoginVerifyOTP(otp_id: $otpId, code: $code) {\n      token\n      user {\n        id\n      }\n    }\n  }\n": types.VerifyOtpDocument,
    "\n   query GetPaymentStatus($orderID: String!) {\n     getPaymentStatus(order_id: $orderID, provider: RZPAY) {\n       status\n     }\n   } \n": types.GetPaymentStatusDocument,
    "\n  query GetMe {\n    getMe {\n      id\n      username\n      mobile_number\n      language\n      email\n      subscription {\n        id\n        user_id\n        plan_id\n        start_date\n        end_date\n        subscription_status\n        plan {\n          id\n          name\n          price\n          duration\n        }\n      }\n    }\n  }\n": types.GetMeDocument,
    "\n  query MovieById($id: ID!) {\n    movieById(id: $id) {\n      id\n      title\n      slug\n      subtitle_path_en\n      subtitle_path_mz\n      description_en\n      description_mz\n      duration\n      age_label\n      casts\n      production_year\n      production_year\n      genre\n      price\n      # trailer_url\n      imagePortrait {\n        id\n        path\n        hash\n      }\n      imageLandscape {\n        id\n        path\n        hash\n      }\n      is_rented\n      cdn_playback_id\n      for_subscriber\n      is_free\n      isAddedToWatchList\n      trailer {\n        cdn_playback_id\n      }\n      continueWatching {\n        watched_duration\n      }\n      ...MovieAction\n    }\n  }\n": types.MovieByIdDocument,
    "\n  query MovieBySlug($slug: String!) {\n    getMovieBySlug(slug: $slug) {\n      id\n      title\n      subtitle_path_en\n      subtitle_path_mz\n      description_en\n      description_mz\n      duration\n      age_label\n      casts\n      production_year\n      production_year\n      genre\n      price\n      # trailer_url\n      imagePortrait {\n        id\n        path\n        hash\n      }\n      imageLandscape {\n        id\n        path\n        hash\n      }\n      is_rented\n      cdn_playback_id\n      for_subscriber\n      is_free\n      isAddedToWatchList\n      trailer {\n        cdn_playback_id\n      }\n      continueWatching {\n        watched_duration\n      }\n      ...MovieAction\n    }\n  }\n": types.MovieBySlugDocument,
    "\n  query SeasonById($id: ID!) {\n    tvShowSeasonById(id: $id) {\n      id\n      title\n      trailer {\n        cdn_playback_id\n      }\n      episodes {\n        __typename\n        ... TVEpisodeFragment\n      }\n      isAddedToWatchList\n      ... ShowAction\n    }\n  }\n": types.SeasonByIdDocument,
    "\n  query ShowByID($id: ID!) {\n    tvShowById(id: $id) {\n      id\n      title\n      description_en\n      description_mz\n      age_label\n      production_year\n      genre\n      casts\n      price_per_episode\n      imageLandscape {\n        id\n        path\n        hash\n      }\n      seasons {\n        id\n        title\n        # trailer_url\n        # price\n        episodes {\n          id\n          cdn_playback_id\n        }\n      }\n    }\n  }\n": types.ShowByIdDocument,
    "\n  query TvShowBySlug($slug: String!) {\n    getTvShowBySlug(slug: $slug) {\n      id\n      title\n      description_en\n      description_mz\n      age_label\n      production_year\n      genre\n      casts\n      price_per_episode\n      imageLandscape {\n        id\n        path\n        hash\n      }\n      seasons {\n        id\n        title\n        # trailer_url\n        # price\n        episodes {\n          id\n          cdn_playback_id\n        }\n      }\n    }\n  }\n": types.TvShowBySlugDocument,
    "\n  query SubscriptionPlans {\n    subscriptionPlans {\n      id\n      name\n      price\n      duration\n    }\n  }\n": types.SubscriptionPlansDocument,
    "\n  query HomeData {\n    getHomeData(first: 20) {\n      data {\n        id\n        category\n        order\n        ...HomeDataFragment\n      }\n    }\n    getContinueWatchingList {\n      ...ContinueWatchingFragment\n    }\n  }\n": types.HomeDataDocument,
    "\n  fragment MovieAction on Movie {\n    id\n    for_subscriber\n    is_rented\n    is_free\n    price\n    continueWatching {\n      watched_duration\n    }\n  }\n": types.MovieActionFragmentDoc,
    "\n  query MovieBySlugServer($slug: String!) {\n    getMovieBySlug(slug: $slug) {\n      id\n      title\n      description_en\n      imageLandscape {\n        path\n      }\n    }\n  }\n": types.MovieBySlugServerDocument,
    "\n  query MyWatchList($page: Int, $first: Int!) {\n    myWatchList(first: $first, page: $page) {\n      data {\n        watch_listable {\n          __typename\n          ...WatchListMovieFragment\n          ...WatchListSeasonFragment\n          ...WatchListEpisodeFragment\n        }\n      }\n      paginatorInfo {\n        lastPage\n        total\n      }\n    }\n  }\n": types.MyWatchListDocument,
    "\n  fragment WatchListEpisodeFragment on TVShowEpisodes {\n    id\n    title\n    description_en\n    description_mz\n    sub_header\n    imageLandscape {\n      id\n      hash\n      path\n    }\n    tv_show_id\n    tv_show_season_id\n    season {\n      title\n    }\n    tvShow {\n      slug\n      title\n    }\n  }\n": types.WatchListEpisodeFragmentFragmentDoc,
    "\n  fragment WatchListMovieFragment on Movie {\n    id\n    slug\n    title\n    description_en\n    description_mz\n    imageLandscape {\n      id\n      hash\n      path\n    }\n  }\n": types.WatchListMovieFragmentFragmentDoc,
    "\n  fragment WatchListSeasonFragment on TVShowSeason {\n    id\n    title\n    description_mz\n    description_en\n    tvShow {\n      slug\n      id\n      title\n      imageLandscape {\n        id\n        hash\n        path\n      }\n    }\n  }\n": types.WatchListSeasonFragmentFragmentDoc,
    "\n  fragment ShowAction on TVShowSeason {\n    id\n    rentable_status\n    is_rented\n    title\n    # price\n    episodes {\n      id\n      ...TVEpisodeFragment\n    }\n  }\n": types.ShowActionFragmentDoc,
    "\n  mutation DeleteAccount {\n    deleteMyAccount {\n      message\n    }\n  }\n": types.DeleteAccountDocument,
    "\n  fragment RentListEpisodeFragment on TVShowEpisodes {\n    id\n    title\n    season {\n      title\n    }\n    tvShow {\n      title\n    }\n  }\n": types.RentListEpisodeFragmentFragmentDoc,
    "\n  fragment RentListLiveFragment on Live {\n    id\n    title\n  }\n": types.RentListLiveFragmentFragmentDoc,
    "\n  fragment RentListMovieFragment on Movie {\n    id\n    title\n  }\n": types.RentListMovieFragmentFragmentDoc,
    "\n  fragment RentListSeasonFragment on TVShowSeason {\n    id\n    title\n    tvShow {\n      title\n    }\n  }\n": types.RentListSeasonFragmentFragmentDoc,
    "\n  query MyContentRentList($page: Int, $first: Int!) {\n    myContentRentList(first: $first, page: $page) {\n      data {\n        id\n        status\n        price\n        start_date\n        end_date\n        rentable {\n          __typename\n          ...RentListEpisodeFragment\n          ...RentListLiveFragment\n          ...RentListMovieFragment\n          ...RentListSeasonFragment\n        }\n      }\n      paginatorInfo {\n        lastPage\n        total\n      }\n    }\n  }\n": types.MyContentRentListDocument,
    "\n  fragment SearchItemLiveFragment on Live {\n    id\n    title\n    slug\n    cdn_playback_id\n    is_rented\n    is_free\n    price\n    for_subscriber\n    imageLandscape {\n      path\n    }\n  }\n": types.SearchItemLiveFragmentFragmentDoc,
    "\n  fragment SearchItemMovieFragment on Movie {\n    id\n    title\n    slug\n    imageLandscape {\n      path\n      hash\n    }\n  }\n": types.SearchItemMovieFragmentFragmentDoc,
    "\n  fragment SearchItemShowFragment on TVShow {\n    id\n    title\n    slug\n    imageLandscape {\n      path\n    }\n  }\n": types.SearchItemShowFragmentFragmentDoc,
    "\n  query SearchContent($keyword: String!) {\n    searchContent(keyword: $keyword) {\n      id\n      canSearch {\n        __typename\n        ...SearchItemMovieFragment\n        ...SearchItemShowFragment\n        ...SearchItemLiveFragment\n        ... on Movie {\n          id\n          title\n          imageLandscape {\n            path\n          }\n        }\n        ... on TVShow {\n          id\n          title\n          imageLandscape {\n            path\n          }\n        }\n        ... on Live {\n          id\n          title\n          cdn_playback_id\n          is_rented\n          is_free\n          price\n          imageLandscape {\n            path\n          }\n        }\n      }\n    }\n  }\n": types.SearchContentDocument,
    "\n  mutation UserLogout {\n    userLogout {\n      message\n    }\n  }\n": types.UserLogoutDocument,
    "\n  mutation UpdateMyInfo($language: String) {\n    updateMyInfo(language: $language) {\n      id\n      language\n    }\n  }\n": types.UpdateMyInfoDocument,
    "\n  query LiveBySlug($slug: String!) {\n    getLiveBySlug(slug: $slug) {\n      id\n      title\n      cdn_playback_id\n      cdn_content_status\n      description_en\n      imageLandscape {\n        path\n      }\n    }\n  }\n": types.LiveBySlugDocument,
    "\n  fragment EpisodePlaylistFragment on TVShowEpisodes {\n    id\n    title\n    cdn_playback_id\n    description_en\n    description_mz\n    is_rented\n    is_free\n    for_subscriber\n    continueWatching {\n      watched_duration\n    }\n    imageLandscape {\n      id\n      hash\n      path\n    }\n  }\n": types.EpisodePlaylistFragmentFragmentDoc,
    "\n  query TvEpisodeById($id: ID!) {\n    tvShowEpisodeById(id: $id) {\n      id\n      title\n      tv_show_id\n      cdn_playback_id\n      description_en\n      continueWatching {\n        watched_duration\n      }\n      imageLandscape {\n        path\n      }\n      season {\n        title\n        episodes {\n          ...EpisodePlaylistFragment\n          cdn_playback_id\n        }\n      }\n      tvShow {\n        title\n        price_per_episode\n      }\n    }\n  }\n": types.TvEpisodeByIdDocument,
    "\n  query Movies($first: Int, $page: Int, $keyword: String) {\n    getMovies(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        title\n        description_en\n        description_mz\n        age_label\n        duration\n        production_year\n        is_rented\n        for_subscriber\n        price\n        is_free\n        imagePortrait {\n          id\n          path\n          hash\n        }\n        imageLandscape {\n          id\n          path\n          hash\n        }\n      }\n      paginatorInfo {\n        hasMorePages\n        total\n        currentPage\n      }\n    }\n  }\n": types.MoviesDocument,
    "\n  query LiveShows($first: Int, $page: Int, $keyword: String) {\n    getLives(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        title\n        imagePortrait {\n          id\n          path\n          hash\n        }\n        imageLandscape {\n          id\n          path\n          hash\n        }\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n": types.LiveShowsDocument,
    "\n  query TvShows($first: Int, $page: Int, $keyword: String) {\n    getTvShows(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        title\n        imagePortrait {\n          id\n          path\n          hash\n        }\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n": types.TvShowsDocument,
    "\n  query TvShowById($id: ID!) {\n    tvShowById(id: $id) {\n      id\n      title\n      price_per_episode\n      description_mz\n      description_en\n      seasons {\n        id\n        title\n        episodes {\n          id\n          title\n          sub_header\n          is_free\n          for_subscriber\n          is_rented\n        }\n      }\n    }\n  }\n": types.TvShowByIdDocument,
    "\n  query GetContentReport($contentType: ReportableType!, $slug: String!) {\n    getContentReport(slug: $slug, content_type: $contentType) {\n      producer_share\n      stats {\n        purchase_count\n        title\n        total_revenue\n      }\n    }\n  }\n": types.GetContentReportDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation RentEpisode($id: ID!, $redirectURL: String) {\n    rentTVShowEpisode(id: $id, redirect_url: $redirectURL) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation RentEpisode($id: ID!, $redirectURL: String) {\n    rentTVShowEpisode(id: $id, redirect_url: $redirectURL) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment ContinueWatchingFragment on WatchHistory {\n    id\n    watchable_id\n    watchable_type\n    watched_duration\n    total_duration\n    user_id\n    watchable {\n      __typename\n      ...TVEpisodeFragment\n      ...MovieFragment\n    }\n  }\n"): (typeof documents)["\n  fragment ContinueWatchingFragment on WatchHistory {\n    id\n    watchable_id\n    watchable_type\n    watched_duration\n    total_duration\n    user_id\n    watchable {\n      __typename\n      ...TVEpisodeFragment\n      ...MovieFragment\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment TVEpisodeFragment on TVShowEpisodes {\n    __typename\n    id\n    cdn_playback_id\n    isAddedToWatchList\n    title\n    sub_header\n    duration\n    is_rented\n    is_free\n    description_en\n    description_mz\n    for_subscriber\n    tv_show_id\n    tv_show_season_id\n    continueWatching {\n      watched_duration\n      total_duration\n    }\n    imageLandscape {\n      id\n      path\n      hash\n    }\n  }\n"): (typeof documents)["\n  fragment TVEpisodeFragment on TVShowEpisodes {\n    __typename\n    id\n    cdn_playback_id\n    isAddedToWatchList\n    title\n    sub_header\n    duration\n    is_rented\n    is_free\n    description_en\n    description_mz\n    for_subscriber\n    tv_show_id\n    tv_show_season_id\n    continueWatching {\n      watched_duration\n      total_duration\n    }\n    imageLandscape {\n      id\n      path\n      hash\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment HomeDataFragment on HomeData {\n    id\n    category\n    order\n    display_type\n    homeDataList {\n      id\n      posterLandscapeImage {\n        id\n        path\n        hash\n      }\n      titleImage {\n        id\n        path\n        hash\n      }\n      homeable {\n        __typename\n        ...TvShowFragment\n        ...LiveShowFragment\n        ...MovieFragment\n      }\n    }\n  }\n"): (typeof documents)["\n  fragment HomeDataFragment on HomeData {\n    id\n    category\n    order\n    display_type\n    homeDataList {\n      id\n      posterLandscapeImage {\n        id\n        path\n        hash\n      }\n      titleImage {\n        id\n        path\n        hash\n      }\n      homeable {\n        __typename\n        ...TvShowFragment\n        ...LiveShowFragment\n        ...MovieFragment\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment LiveShowFragment on Live {\n    id\n    title\n    slug\n    cdn_playback_id\n    cdn_content_status\n    is_rented\n    is_free\n    for_subscriber\n    price\n    description_en\n    description_mz\n    imageLandscape {\n      id\n      path\n      hash\n    }\n    imagePortrait {\n      id\n      path\n      hash\n    }\n  }\n"): (typeof documents)["\n  fragment LiveShowFragment on Live {\n    id\n    title\n    slug\n    cdn_playback_id\n    cdn_content_status\n    is_rented\n    is_free\n    for_subscriber\n    price\n    description_en\n    description_mz\n    imageLandscape {\n      id\n      path\n      hash\n    }\n    imagePortrait {\n      id\n      path\n      hash\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment MovieFragment on Movie {\n    id\n    title\n    description_en\n    description_mz\n    production_year\n    age_label\n    duration\n    genre\n    slug\n    imageLandscape {\n      id\n      path\n      hash\n    }\n    imagePortrait {\n      id\n      path\n      hash\n    }\n    imageSquare {\n      id\n      path\n      hash\n    }\n    imageSquare2x {\n      id\n      path\n      hash\n    }\n    imagePortrait2x {\n      id\n      path\n      hash\n    }\n  }\n"): (typeof documents)["\n  fragment MovieFragment on Movie {\n    id\n    title\n    description_en\n    description_mz\n    production_year\n    age_label\n    duration\n    genre\n    slug\n    imageLandscape {\n      id\n      path\n      hash\n    }\n    imagePortrait {\n      id\n      path\n      hash\n    }\n    imageSquare {\n      id\n      path\n      hash\n    }\n    imageSquare2x {\n      id\n      path\n      hash\n    }\n    imagePortrait2x {\n      id\n      path\n      hash\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment TvShowFragment on TVShow {\n    id\n    title\n    slug\n    description_en\n    description_mz\n    production_year\n    genre\n    age_label\n    imageLandscape {\n      id\n      path\n      hash\n    }\n    imagePortrait {\n      id\n      path\n      hash\n    }\n    imageSquare {\n      id\n      path\n      hash\n    }\n    imagePortrait2x {\n      id\n      path\n      hash\n    }\n    imageSquare2x {\n      id\n      path\n      hash\n    }\n  }\n"): (typeof documents)["\n  fragment TvShowFragment on TVShow {\n    id\n    title\n    slug\n    description_en\n    description_mz\n    production_year\n    genre\n    age_label\n    imageLandscape {\n      id\n      path\n      hash\n    }\n    imagePortrait {\n      id\n      path\n      hash\n    }\n    imageSquare {\n      id\n      path\n      hash\n    }\n    imagePortrait2x {\n      id\n      path\n      hash\n    }\n    imageSquare2x {\n      id\n      path\n      hash\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddWatchList($id: ID!, $contentType: WatchListableType) {\n    addWatchList(id: $id, content_type: $contentType) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation AddWatchList($id: ID!, $contentType: WatchListableType) {\n    addWatchList(id: $id, content_type: $contentType) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteMyWatchList($id: ID!) {\n    deleteMyWatchList(id: $id) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteMyWatchList($id: ID!) {\n    deleteMyWatchList(id: $id) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation GenerateContentUrl(\n    $id: ID!\n    $contentType: WatchableContentTypes!\n  ) {\n    generateContentUrl(\n      id: $id\n      content_type: $contentType\n    ) {\n      url\n      story_board_url\n    }\n  }\n"): (typeof documents)["\n  mutation GenerateContentUrl(\n    $id: ID!\n    $contentType: WatchableContentTypes!\n  ) {\n    generateContentUrl(\n      id: $id\n      content_type: $contentType\n    ) {\n      url\n      story_board_url\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation RentLive($id: ID!, $redirectURL: String) {\n    rentLive(id: $id, redirect_url: $redirectURL) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation RentLive($id: ID!, $redirectURL: String) {\n    rentLive(id: $id, redirect_url: $redirectURL) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation RentMovie($id: ID!, $redirectURL: String) {\n    rentMovie(id: $id, redirect_url: $redirectURL) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation RentMovie($id: ID!, $redirectURL: String) {\n    rentMovie(id: $id, redirect_url: $redirectURL) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation RentMultipleEpisodes($ids: [ID!]!, $totalAmount: Float!) {\n    rentMultipleEpisodes(ids: $ids, total_amount: $totalAmount) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation RentMultipleEpisodes($ids: [ID!]!, $totalAmount: Float!) {\n    rentMultipleEpisodes(ids: $ids, total_amount: $totalAmount) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation SubscribeToPlan($planId: ID!, $redirectUrl: String) {\n    subscribeToPlan(plan_id: $planId, redirect_url: $redirectUrl) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n        price\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation SubscribeToPlan($planId: ID!, $redirectUrl: String) {\n    subscribeToPlan(plan_id: $planId, redirect_url: $redirectUrl) {\n      __typename\n      ... on PhonePePaymentResponse {\n        goto_url\n      }\n      ... on RazorPayPaymentResponse {\n        order_id\n        price\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpsertWatchedDuration(\n    $contentId: ID!\n    $contentType: WatchDurationContentType!\n    $duration: Int!\n    $totalDuration: Int!\n  ) {\n    upsertWatchedDuration(\n      content_id: $contentId\n      content_type: $contentType\n      duration: $duration\n      total_duration: $totalDuration\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation UpsertWatchedDuration(\n    $contentId: ID!\n    $contentType: WatchDurationContentType!\n    $duration: Int!\n    $totalDuration: Int!\n  ) {\n    upsertWatchedDuration(\n      content_id: $contentId\n      content_type: $contentType\n      duration: $duration\n      total_duration: $totalDuration\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UserLogin($mobileNumber: String, $email: String) {\n    userLogin(mobile_number: $mobileNumber, email: $email) {\n      otp_id\n    }\n  }\n"): (typeof documents)["\n  mutation UserLogin($mobileNumber: String, $email: String) {\n    userLogin(mobile_number: $mobileNumber, email: $email) {\n      otp_id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation VerifyOtp($otpId: Int!, $code: Int!) {\n    userLoginVerifyOTP(otp_id: $otpId, code: $code) {\n      token\n      user {\n        id\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation VerifyOtp($otpId: Int!, $code: Int!) {\n    userLoginVerifyOTP(otp_id: $otpId, code: $code) {\n      token\n      user {\n        id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n   query GetPaymentStatus($orderID: String!) {\n     getPaymentStatus(order_id: $orderID, provider: RZPAY) {\n       status\n     }\n   } \n"): (typeof documents)["\n   query GetPaymentStatus($orderID: String!) {\n     getPaymentStatus(order_id: $orderID, provider: RZPAY) {\n       status\n     }\n   } \n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetMe {\n    getMe {\n      id\n      username\n      mobile_number\n      language\n      email\n      subscription {\n        id\n        user_id\n        plan_id\n        start_date\n        end_date\n        subscription_status\n        plan {\n          id\n          name\n          price\n          duration\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetMe {\n    getMe {\n      id\n      username\n      mobile_number\n      language\n      email\n      subscription {\n        id\n        user_id\n        plan_id\n        start_date\n        end_date\n        subscription_status\n        plan {\n          id\n          name\n          price\n          duration\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query MovieById($id: ID!) {\n    movieById(id: $id) {\n      id\n      title\n      slug\n      subtitle_path_en\n      subtitle_path_mz\n      description_en\n      description_mz\n      duration\n      age_label\n      casts\n      production_year\n      production_year\n      genre\n      price\n      # trailer_url\n      imagePortrait {\n        id\n        path\n        hash\n      }\n      imageLandscape {\n        id\n        path\n        hash\n      }\n      is_rented\n      cdn_playback_id\n      for_subscriber\n      is_free\n      isAddedToWatchList\n      trailer {\n        cdn_playback_id\n      }\n      continueWatching {\n        watched_duration\n      }\n      ...MovieAction\n    }\n  }\n"): (typeof documents)["\n  query MovieById($id: ID!) {\n    movieById(id: $id) {\n      id\n      title\n      slug\n      subtitle_path_en\n      subtitle_path_mz\n      description_en\n      description_mz\n      duration\n      age_label\n      casts\n      production_year\n      production_year\n      genre\n      price\n      # trailer_url\n      imagePortrait {\n        id\n        path\n        hash\n      }\n      imageLandscape {\n        id\n        path\n        hash\n      }\n      is_rented\n      cdn_playback_id\n      for_subscriber\n      is_free\n      isAddedToWatchList\n      trailer {\n        cdn_playback_id\n      }\n      continueWatching {\n        watched_duration\n      }\n      ...MovieAction\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query MovieBySlug($slug: String!) {\n    getMovieBySlug(slug: $slug) {\n      id\n      title\n      subtitle_path_en\n      subtitle_path_mz\n      description_en\n      description_mz\n      duration\n      age_label\n      casts\n      production_year\n      production_year\n      genre\n      price\n      # trailer_url\n      imagePortrait {\n        id\n        path\n        hash\n      }\n      imageLandscape {\n        id\n        path\n        hash\n      }\n      is_rented\n      cdn_playback_id\n      for_subscriber\n      is_free\n      isAddedToWatchList\n      trailer {\n        cdn_playback_id\n      }\n      continueWatching {\n        watched_duration\n      }\n      ...MovieAction\n    }\n  }\n"): (typeof documents)["\n  query MovieBySlug($slug: String!) {\n    getMovieBySlug(slug: $slug) {\n      id\n      title\n      subtitle_path_en\n      subtitle_path_mz\n      description_en\n      description_mz\n      duration\n      age_label\n      casts\n      production_year\n      production_year\n      genre\n      price\n      # trailer_url\n      imagePortrait {\n        id\n        path\n        hash\n      }\n      imageLandscape {\n        id\n        path\n        hash\n      }\n      is_rented\n      cdn_playback_id\n      for_subscriber\n      is_free\n      isAddedToWatchList\n      trailer {\n        cdn_playback_id\n      }\n      continueWatching {\n        watched_duration\n      }\n      ...MovieAction\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query SeasonById($id: ID!) {\n    tvShowSeasonById(id: $id) {\n      id\n      title\n      trailer {\n        cdn_playback_id\n      }\n      episodes {\n        __typename\n        ... TVEpisodeFragment\n      }\n      isAddedToWatchList\n      ... ShowAction\n    }\n  }\n"): (typeof documents)["\n  query SeasonById($id: ID!) {\n    tvShowSeasonById(id: $id) {\n      id\n      title\n      trailer {\n        cdn_playback_id\n      }\n      episodes {\n        __typename\n        ... TVEpisodeFragment\n      }\n      isAddedToWatchList\n      ... ShowAction\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ShowByID($id: ID!) {\n    tvShowById(id: $id) {\n      id\n      title\n      description_en\n      description_mz\n      age_label\n      production_year\n      genre\n      casts\n      price_per_episode\n      imageLandscape {\n        id\n        path\n        hash\n      }\n      seasons {\n        id\n        title\n        # trailer_url\n        # price\n        episodes {\n          id\n          cdn_playback_id\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query ShowByID($id: ID!) {\n    tvShowById(id: $id) {\n      id\n      title\n      description_en\n      description_mz\n      age_label\n      production_year\n      genre\n      casts\n      price_per_episode\n      imageLandscape {\n        id\n        path\n        hash\n      }\n      seasons {\n        id\n        title\n        # trailer_url\n        # price\n        episodes {\n          id\n          cdn_playback_id\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query TvShowBySlug($slug: String!) {\n    getTvShowBySlug(slug: $slug) {\n      id\n      title\n      description_en\n      description_mz\n      age_label\n      production_year\n      genre\n      casts\n      price_per_episode\n      imageLandscape {\n        id\n        path\n        hash\n      }\n      seasons {\n        id\n        title\n        # trailer_url\n        # price\n        episodes {\n          id\n          cdn_playback_id\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query TvShowBySlug($slug: String!) {\n    getTvShowBySlug(slug: $slug) {\n      id\n      title\n      description_en\n      description_mz\n      age_label\n      production_year\n      genre\n      casts\n      price_per_episode\n      imageLandscape {\n        id\n        path\n        hash\n      }\n      seasons {\n        id\n        title\n        # trailer_url\n        # price\n        episodes {\n          id\n          cdn_playback_id\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query SubscriptionPlans {\n    subscriptionPlans {\n      id\n      name\n      price\n      duration\n    }\n  }\n"): (typeof documents)["\n  query SubscriptionPlans {\n    subscriptionPlans {\n      id\n      name\n      price\n      duration\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query HomeData {\n    getHomeData(first: 20) {\n      data {\n        id\n        category\n        order\n        ...HomeDataFragment\n      }\n    }\n    getContinueWatchingList {\n      ...ContinueWatchingFragment\n    }\n  }\n"): (typeof documents)["\n  query HomeData {\n    getHomeData(first: 20) {\n      data {\n        id\n        category\n        order\n        ...HomeDataFragment\n      }\n    }\n    getContinueWatchingList {\n      ...ContinueWatchingFragment\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment MovieAction on Movie {\n    id\n    for_subscriber\n    is_rented\n    is_free\n    price\n    continueWatching {\n      watched_duration\n    }\n  }\n"): (typeof documents)["\n  fragment MovieAction on Movie {\n    id\n    for_subscriber\n    is_rented\n    is_free\n    price\n    continueWatching {\n      watched_duration\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query MovieBySlugServer($slug: String!) {\n    getMovieBySlug(slug: $slug) {\n      id\n      title\n      description_en\n      imageLandscape {\n        path\n      }\n    }\n  }\n"): (typeof documents)["\n  query MovieBySlugServer($slug: String!) {\n    getMovieBySlug(slug: $slug) {\n      id\n      title\n      description_en\n      imageLandscape {\n        path\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query MyWatchList($page: Int, $first: Int!) {\n    myWatchList(first: $first, page: $page) {\n      data {\n        watch_listable {\n          __typename\n          ...WatchListMovieFragment\n          ...WatchListSeasonFragment\n          ...WatchListEpisodeFragment\n        }\n      }\n      paginatorInfo {\n        lastPage\n        total\n      }\n    }\n  }\n"): (typeof documents)["\n  query MyWatchList($page: Int, $first: Int!) {\n    myWatchList(first: $first, page: $page) {\n      data {\n        watch_listable {\n          __typename\n          ...WatchListMovieFragment\n          ...WatchListSeasonFragment\n          ...WatchListEpisodeFragment\n        }\n      }\n      paginatorInfo {\n        lastPage\n        total\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment WatchListEpisodeFragment on TVShowEpisodes {\n    id\n    title\n    description_en\n    description_mz\n    sub_header\n    imageLandscape {\n      id\n      hash\n      path\n    }\n    tv_show_id\n    tv_show_season_id\n    season {\n      title\n    }\n    tvShow {\n      slug\n      title\n    }\n  }\n"): (typeof documents)["\n  fragment WatchListEpisodeFragment on TVShowEpisodes {\n    id\n    title\n    description_en\n    description_mz\n    sub_header\n    imageLandscape {\n      id\n      hash\n      path\n    }\n    tv_show_id\n    tv_show_season_id\n    season {\n      title\n    }\n    tvShow {\n      slug\n      title\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment WatchListMovieFragment on Movie {\n    id\n    slug\n    title\n    description_en\n    description_mz\n    imageLandscape {\n      id\n      hash\n      path\n    }\n  }\n"): (typeof documents)["\n  fragment WatchListMovieFragment on Movie {\n    id\n    slug\n    title\n    description_en\n    description_mz\n    imageLandscape {\n      id\n      hash\n      path\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment WatchListSeasonFragment on TVShowSeason {\n    id\n    title\n    description_mz\n    description_en\n    tvShow {\n      slug\n      id\n      title\n      imageLandscape {\n        id\n        hash\n        path\n      }\n    }\n  }\n"): (typeof documents)["\n  fragment WatchListSeasonFragment on TVShowSeason {\n    id\n    title\n    description_mz\n    description_en\n    tvShow {\n      slug\n      id\n      title\n      imageLandscape {\n        id\n        hash\n        path\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment ShowAction on TVShowSeason {\n    id\n    rentable_status\n    is_rented\n    title\n    # price\n    episodes {\n      id\n      ...TVEpisodeFragment\n    }\n  }\n"): (typeof documents)["\n  fragment ShowAction on TVShowSeason {\n    id\n    rentable_status\n    is_rented\n    title\n    # price\n    episodes {\n      id\n      ...TVEpisodeFragment\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteAccount {\n    deleteMyAccount {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteAccount {\n    deleteMyAccount {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment RentListEpisodeFragment on TVShowEpisodes {\n    id\n    title\n    season {\n      title\n    }\n    tvShow {\n      title\n    }\n  }\n"): (typeof documents)["\n  fragment RentListEpisodeFragment on TVShowEpisodes {\n    id\n    title\n    season {\n      title\n    }\n    tvShow {\n      title\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment RentListLiveFragment on Live {\n    id\n    title\n  }\n"): (typeof documents)["\n  fragment RentListLiveFragment on Live {\n    id\n    title\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment RentListMovieFragment on Movie {\n    id\n    title\n  }\n"): (typeof documents)["\n  fragment RentListMovieFragment on Movie {\n    id\n    title\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment RentListSeasonFragment on TVShowSeason {\n    id\n    title\n    tvShow {\n      title\n    }\n  }\n"): (typeof documents)["\n  fragment RentListSeasonFragment on TVShowSeason {\n    id\n    title\n    tvShow {\n      title\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query MyContentRentList($page: Int, $first: Int!) {\n    myContentRentList(first: $first, page: $page) {\n      data {\n        id\n        status\n        price\n        start_date\n        end_date\n        rentable {\n          __typename\n          ...RentListEpisodeFragment\n          ...RentListLiveFragment\n          ...RentListMovieFragment\n          ...RentListSeasonFragment\n        }\n      }\n      paginatorInfo {\n        lastPage\n        total\n      }\n    }\n  }\n"): (typeof documents)["\n  query MyContentRentList($page: Int, $first: Int!) {\n    myContentRentList(first: $first, page: $page) {\n      data {\n        id\n        status\n        price\n        start_date\n        end_date\n        rentable {\n          __typename\n          ...RentListEpisodeFragment\n          ...RentListLiveFragment\n          ...RentListMovieFragment\n          ...RentListSeasonFragment\n        }\n      }\n      paginatorInfo {\n        lastPage\n        total\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment SearchItemLiveFragment on Live {\n    id\n    title\n    slug\n    cdn_playback_id\n    is_rented\n    is_free\n    price\n    for_subscriber\n    imageLandscape {\n      path\n    }\n  }\n"): (typeof documents)["\n  fragment SearchItemLiveFragment on Live {\n    id\n    title\n    slug\n    cdn_playback_id\n    is_rented\n    is_free\n    price\n    for_subscriber\n    imageLandscape {\n      path\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment SearchItemMovieFragment on Movie {\n    id\n    title\n    slug\n    imageLandscape {\n      path\n      hash\n    }\n  }\n"): (typeof documents)["\n  fragment SearchItemMovieFragment on Movie {\n    id\n    title\n    slug\n    imageLandscape {\n      path\n      hash\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment SearchItemShowFragment on TVShow {\n    id\n    title\n    slug\n    imageLandscape {\n      path\n    }\n  }\n"): (typeof documents)["\n  fragment SearchItemShowFragment on TVShow {\n    id\n    title\n    slug\n    imageLandscape {\n      path\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query SearchContent($keyword: String!) {\n    searchContent(keyword: $keyword) {\n      id\n      canSearch {\n        __typename\n        ...SearchItemMovieFragment\n        ...SearchItemShowFragment\n        ...SearchItemLiveFragment\n        ... on Movie {\n          id\n          title\n          imageLandscape {\n            path\n          }\n        }\n        ... on TVShow {\n          id\n          title\n          imageLandscape {\n            path\n          }\n        }\n        ... on Live {\n          id\n          title\n          cdn_playback_id\n          is_rented\n          is_free\n          price\n          imageLandscape {\n            path\n          }\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query SearchContent($keyword: String!) {\n    searchContent(keyword: $keyword) {\n      id\n      canSearch {\n        __typename\n        ...SearchItemMovieFragment\n        ...SearchItemShowFragment\n        ...SearchItemLiveFragment\n        ... on Movie {\n          id\n          title\n          imageLandscape {\n            path\n          }\n        }\n        ... on TVShow {\n          id\n          title\n          imageLandscape {\n            path\n          }\n        }\n        ... on Live {\n          id\n          title\n          cdn_playback_id\n          is_rented\n          is_free\n          price\n          imageLandscape {\n            path\n          }\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UserLogout {\n    userLogout {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation UserLogout {\n    userLogout {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateMyInfo($language: String) {\n    updateMyInfo(language: $language) {\n      id\n      language\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateMyInfo($language: String) {\n    updateMyInfo(language: $language) {\n      id\n      language\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query LiveBySlug($slug: String!) {\n    getLiveBySlug(slug: $slug) {\n      id\n      title\n      cdn_playback_id\n      cdn_content_status\n      description_en\n      imageLandscape {\n        path\n      }\n    }\n  }\n"): (typeof documents)["\n  query LiveBySlug($slug: String!) {\n    getLiveBySlug(slug: $slug) {\n      id\n      title\n      cdn_playback_id\n      cdn_content_status\n      description_en\n      imageLandscape {\n        path\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment EpisodePlaylistFragment on TVShowEpisodes {\n    id\n    title\n    cdn_playback_id\n    description_en\n    description_mz\n    is_rented\n    is_free\n    for_subscriber\n    continueWatching {\n      watched_duration\n    }\n    imageLandscape {\n      id\n      hash\n      path\n    }\n  }\n"): (typeof documents)["\n  fragment EpisodePlaylistFragment on TVShowEpisodes {\n    id\n    title\n    cdn_playback_id\n    description_en\n    description_mz\n    is_rented\n    is_free\n    for_subscriber\n    continueWatching {\n      watched_duration\n    }\n    imageLandscape {\n      id\n      hash\n      path\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query TvEpisodeById($id: ID!) {\n    tvShowEpisodeById(id: $id) {\n      id\n      title\n      tv_show_id\n      cdn_playback_id\n      description_en\n      continueWatching {\n        watched_duration\n      }\n      imageLandscape {\n        path\n      }\n      season {\n        title\n        episodes {\n          ...EpisodePlaylistFragment\n          cdn_playback_id\n        }\n      }\n      tvShow {\n        title\n        price_per_episode\n      }\n    }\n  }\n"): (typeof documents)["\n  query TvEpisodeById($id: ID!) {\n    tvShowEpisodeById(id: $id) {\n      id\n      title\n      tv_show_id\n      cdn_playback_id\n      description_en\n      continueWatching {\n        watched_duration\n      }\n      imageLandscape {\n        path\n      }\n      season {\n        title\n        episodes {\n          ...EpisodePlaylistFragment\n          cdn_playback_id\n        }\n      }\n      tvShow {\n        title\n        price_per_episode\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Movies($first: Int, $page: Int, $keyword: String) {\n    getMovies(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        title\n        description_en\n        description_mz\n        age_label\n        duration\n        production_year\n        is_rented\n        for_subscriber\n        price\n        is_free\n        imagePortrait {\n          id\n          path\n          hash\n        }\n        imageLandscape {\n          id\n          path\n          hash\n        }\n      }\n      paginatorInfo {\n        hasMorePages\n        total\n        currentPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query Movies($first: Int, $page: Int, $keyword: String) {\n    getMovies(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        title\n        description_en\n        description_mz\n        age_label\n        duration\n        production_year\n        is_rented\n        for_subscriber\n        price\n        is_free\n        imagePortrait {\n          id\n          path\n          hash\n        }\n        imageLandscape {\n          id\n          path\n          hash\n        }\n      }\n      paginatorInfo {\n        hasMorePages\n        total\n        currentPage\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query LiveShows($first: Int, $page: Int, $keyword: String) {\n    getLives(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        title\n        imagePortrait {\n          id\n          path\n          hash\n        }\n        imageLandscape {\n          id\n          path\n          hash\n        }\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query LiveShows($first: Int, $page: Int, $keyword: String) {\n    getLives(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        title\n        imagePortrait {\n          id\n          path\n          hash\n        }\n        imageLandscape {\n          id\n          path\n          hash\n        }\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query TvShows($first: Int, $page: Int, $keyword: String) {\n    getTvShows(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        title\n        imagePortrait {\n          id\n          path\n          hash\n        }\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query TvShows($first: Int, $page: Int, $keyword: String) {\n    getTvShows(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        title\n        imagePortrait {\n          id\n          path\n          hash\n        }\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query TvShowById($id: ID!) {\n    tvShowById(id: $id) {\n      id\n      title\n      price_per_episode\n      description_mz\n      description_en\n      seasons {\n        id\n        title\n        episodes {\n          id\n          title\n          sub_header\n          is_free\n          for_subscriber\n          is_rented\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query TvShowById($id: ID!) {\n    tvShowById(id: $id) {\n      id\n      title\n      price_per_episode\n      description_mz\n      description_en\n      seasons {\n        id\n        title\n        episodes {\n          id\n          title\n          sub_header\n          is_free\n          for_subscriber\n          is_rented\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetContentReport($contentType: ReportableType!, $slug: String!) {\n    getContentReport(slug: $slug, content_type: $contentType) {\n      producer_share\n      stats {\n        purchase_count\n        title\n        total_revenue\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetContentReport($contentType: ReportableType!, $slug: String!) {\n    getContentReport(slug: $slug, content_type: $contentType) {\n      producer_share\n      stats {\n        purchase_count\n        title\n        total_revenue\n      }\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;