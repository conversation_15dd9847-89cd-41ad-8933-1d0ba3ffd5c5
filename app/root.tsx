import {
  Links,
  Meta,
  NavLink,
  Outlet,
  Scripts,
  ScrollRestoration,
  isRouteErrorResponse,
  useRouteError,
  type LinksFunction,
} from "react-router"
import { useState } from "react"
import stylesheet from "./tailwind.css?url"
import duration from "dayjs/plugin/duration"
import advancedFormat from "dayjs/plugin/advancedFormat"
import dayjs from "dayjs"
import { Toaster } from "./components/ui/toaster"
import { ClientError } from "graphql-request"
import { QueryClient, QueryCache, MutationCache, QueryClientProvider } from "@tanstack/react-query"

export const links: LinksFunction = () => [{ rel: "stylesheet", href: stylesheet }]

dayjs.extend(duration)
dayjs.extend(advancedFormat)

export default function App() {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000,
          },
        },
        queryCache: new QueryCache({
          onError: (error) => {
            if (error instanceof ClientError) {
              error?.response?.errors?.map((error) => {
                if (error.message === "Unauthenticated.") {
                  window.location.href = `/login?ref=${window.location.pathname}${window.location.search}`
                }

                // subscription required
                if (error?.extensions?.error_code === 100) {
                  window.location.href = `/subscription?ref=${window.location.pathname}${window.location.search}`
                }
              })
            }
          },
        }),
        mutationCache: new MutationCache({
          onError: (error) => {
            if (error instanceof ClientError) {
              error?.response?.errors?.map((error) => {
                if (error.message === "Unauthenticated.") {
                  window.location.href = `/login?ref=${window.location.pathname}${window.location.search}`
                }

                // subscription required
                if (error?.extensions?.error_code === 100) {
                  window.location.href = `/subscription?ref=${window.location.pathname}${window.location.search}`
                }
              })
            }
          },
        }),
      })
  )

  return (
    <html lang="en" className="dark">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
        <Meta />
        <Links />
      </head>
      <body>
        <QueryClientProvider client={queryClient}>
          <Outlet />
          <Toaster />
        </QueryClientProvider>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  )
}

export function ErrorBoundary() {
  const error = useRouteError()

  console.log("error boundary error", error)
  if (isRouteErrorResponse(error)) {
    return (
      <html lang="en" className="dark">
        <head>
          <title>Oh no! Enila has run into an error</title>
          <Meta />
          <Links />
        </head>
        <body>
          <div className="flex h-96 flex-col items-center justify-center gap-y-4">
            <h1 className="text-center text-4xl font-bold">
              {error.status === 404
                ? `${error.status} Page ${error.statusText}`
                : `${error.status} ${error.statusText}`}
            </h1>
            <NavLink className="underline" to="/">
              Go Home.
            </NavLink>
          </div>
          <Scripts />
        </body>
      </html>
    )
  } else if (error instanceof Error) {
    console.log("error instance of error", error)
    return (
      <html lang="en" className="dark">
        <head>
          <title>Oh no! Enila has run into an error</title>
          <Meta />
          <Links />
        </head>
        <body>
          <div className="flex h-full min-h-screen flex-col">
            <main className="grow">
              <div className="flex h-96 flex-col items-center justify-center gap-y-4">
                {error.message.includes("status code 503") ? (
                  <div className="flex flex-col items-center">
                    <img src="/enila_logo.png" alt="logo" className="w-24" />
                    <div className="mt-16 text-center text-2xl font-bold">
                      Website is currently under maintenance.
                    </div>
                    <div className="text-center text-2xl font-bold">Please come back later.</div>
                  </div>
                ) : error?.message ? (
                  <div className="flex flex-col items-center">
                    <img src="/enila_logo.png" alt="logo" className="w-24" />
                    <h1 className="mt-16 text-center text-4xl font-bold">{error.message}</h1>
                  </div>
                ) : (
                  <div className="flex flex-col items-center">
                    <img src="/enila_logo.png" alt="logo" className="w-24" />
                    <h1 className="mt-16 text-center text-4xl font-bold">
                      We are currently facing issues with the website.
                    </h1>
                    <div className="mt-4 text-center text-3xl font-bold">
                      Please come back later.
                    </div>
                  </div>
                )}
              </div>
            </main>
            <footer className="mx-auto h-auto w-full px-2 pb-16 pt-8">
              <div className="mt-16 flex flex-col items-center">
                <span>A product of </span>
                {/* <img src="/arsi_logo.svg" alt="Arsi logo" className="size-12" /> */}
                <a
                  href="https://arsi.in"
                  target="_blank"
                  rel="noreferrer noopener"
                  className="hover:underline"
                >
                  Arsi Consultancy
                </a>
              </div>
            </footer>
          </div>
          <Scripts />
        </body>
      </html>
    )
  } else {
    return (
      <html lang="en" className="dark">
        <head>
          <title>Oh no! Enila has run into an error</title>
          <Meta />
          <Links />
        </head>
        <body>
          <div className="flex h-96 flex-col items-center justify-center gap-y-4">
            {/* <ErrorComponent /> */}
          </div>
          <Scripts />
        </body>
      </html>
    )
  }
}
