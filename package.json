{"name": "web-enila", "private": true, "sideEffects": false, "type": "module", "scripts": {"build:production": "react-router build", "build:staging": "NODE_ENV=test react-router build", "dev": "react-router dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start:production": "PORT=3000 react-router-serve ./build/server/index.js", "start:staging": "NODE_ENV=test PORT=3001 react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc", "codegen": "graphql-codegen", "typegen": "react-router typegen --watch"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@mux/mux-player-react": "^3.2.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.4", "@react-router/node": "^7.1.1", "@react-router/serve": "^7.1.1", "@tanstack/react-query": "^5.62.7", "blurhash": "^2.0.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "dayjs": "^1.11.13", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "graphql": "^16.10.0", "graphql-request": "^7.1.2", "input-otp": "^1.4.2", "isbot": "^5.1.21", "js-cookie": "^3.0.5", "lucide-react": "^0.469.0", "media-chrome": "^4.7.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-router": "^7.1.1", "recharts": "^2.15.0", "remix-utils": "^8.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.0", "use-debounce": "^10.0.4", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.3", "@graphql-codegen/client-preset": "^4.5.1", "@react-router/dev": "^7.1.1", "@react-router/fs-routes": "^7.1.1", "@types/js-cookie": "^3.0.6", "@types/react": "^19.0.4", "@types/react-dom": "^19.0.2", "@types/ua-parser-js": "^0.7.39", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-tailwindcss": "^3.17.5", "globals": "^15.14.0", "postcss": "^8.4.49", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5.7.3", "vite": "^6.0.7", "vite-tsconfig-paths": "^5.1.4"}, "volta": {"node": "22.11.0"}}