{
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/.server/**/*.ts",
    "**/.server/**/*.tsx",
    "**/.client/**/*.ts",
    "**/.client/**/*.tsx",
    ".react-router/types/**/*"
  ],
  "compilerOptions": {
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "types": ["@react-router/node", "vite/client"],
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "target": "ES2022",
    "strict": true,
    "allowJs": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "verbatimModuleSyntax": true,
    "paths": {
      "@/*": ["./app/*"],
      "@components/*": [
        "./app/components/*"
      ],
      "@lib/*": [
        "./app/lib/*"
      ]
    },
    "rootDirs": [".", "./.react-router/types"],

    // Vite takes care of building everything, not tsc.
    "noEmit": true
  }
}
