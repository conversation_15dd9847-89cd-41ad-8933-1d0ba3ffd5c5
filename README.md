# Welcome to Remix + Vite!

📖 See the [Remix docs](https://remix.run/docs) and the [Remix Vite docs](https://remix.run/docs/en/main/guides/vite) for details on supported features.

## Development

Run the Vite dev server:

```shellscript
npm run dev
```

## Deployment for staging

First, build the app for staging:

```sh
npm run build:staging
```

Then run the app in staging mode:

```sh
npm run start:staging
```

## Deployment for production

First, build your app for production:

```sh
npm run build:production
```

Then run the app in production mode:

```sh
npm run start:production
```

Now you'll need to pick a host to deploy it to.

### DIY

If you're familiar with deploying Node applications, the built-in Remix app server is production-ready.

Make sure to deploy the output of `npm run build`

- `build/server`
- `build/client`
